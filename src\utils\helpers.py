"""
Helper utilities for data processing and analysis
"""
import re
import time
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import pandas as pd
from textblob import TextBlob

def clean_text(text: str) -> str:
    """
    Clean and normalize text data
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove special characters but keep spaces and hyphens
    text = re.sub(r'[^\w\s\-]', ' ', text)
    
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    return text.strip()

def extract_keywords(text: str, min_length: int = 3) -> List[str]:
    """
    Extract meaningful keywords from text
    
    Args:
        text: Text to extract keywords from
        min_length: Minimum keyword length
        
    Returns:
        List of keywords
    """
    if not text:
        return []
    
    # Clean text
    cleaned = clean_text(text)
    
    # Split into words
    words = cleaned.split()
    
    # Filter words
    keywords = []
    for word in words:
        if len(word) >= min_length and word.isalpha():
            keywords.append(word)
    
    return list(set(keywords))  # Remove duplicates

def calculate_trend_score(
    etsy_data: Dict[str, Any],
    tiktok_data: Dict[str, Any],
    google_data: Dict[str, Any],
    weights: Dict[str, float] = None
) -> float:
    """
    Calculate composite trend score from multiple data sources
    
    Args:
        etsy_data: Etsy metrics
        tiktok_data: TikTok metrics  
        google_data: Google Trends metrics
        weights: Platform weights for scoring
        
    Returns:
        Trend score between 0 and 1
    """
    if weights is None:
        weights = {'etsy': 0.4, 'tiktok': 0.3, 'google': 0.3}
    
    scores = []
    
    # Etsy score (based on search results and sales)
    if etsy_data:
        etsy_score = min(1.0, (etsy_data.get('results_count', 0) / 1000) * 0.7 + 
                        (etsy_data.get('avg_sales', 0) / 100) * 0.3)
        scores.append(etsy_score * weights['etsy'])
    
    # TikTok score (based on hashtag usage and engagement)
    if tiktok_data:
        tiktok_score = min(1.0, (tiktok_data.get('video_count', 0) / 10000) * 0.6 +
                          (tiktok_data.get('avg_views', 0) / 1000000) * 0.4)
        scores.append(tiktok_score * weights['tiktok'])
    
    # Google Trends score (based on search interest)
    if google_data:
        google_score = google_data.get('interest', 0) / 100
        scores.append(google_score * weights['google'])
    
    return sum(scores) if scores else 0.0

def normalize_keyword(keyword: str) -> str:
    """
    Normalize keyword for consistent comparison
    
    Args:
        keyword: Raw keyword
        
    Returns:
        Normalized keyword
    """
    # Clean and convert to lowercase
    normalized = clean_text(keyword)
    
    # Handle common variations
    variations = {
        'tshirt': 't-shirt',
        't shirt': 't-shirt',
        'tee shirt': 't-shirt',
        'coffee mug': 'mug',
        'coffee cup': 'mug'
    }
    
    for variant, standard in variations.items():
        if variant in normalized:
            normalized = normalized.replace(variant, standard)
    
    return normalized

def rate_limit_delay(last_call_time: float, min_interval: float) -> None:
    """
    Implement rate limiting delay
    
    Args:
        last_call_time: Timestamp of last API call
        min_interval: Minimum interval between calls in seconds
    """
    current_time = time.time()
    elapsed = current_time - last_call_time
    
    if elapsed < min_interval:
        sleep_time = min_interval - elapsed
        time.sleep(sleep_time)

def generate_hash(data: str) -> str:
    """
    Generate hash for data deduplication
    
    Args:
        data: String data to hash
        
    Returns:
        MD5 hash string
    """
    return hashlib.md5(data.encode()).hexdigest()

def is_recent(timestamp: datetime, hours: int = 24) -> bool:
    """
    Check if timestamp is within recent hours
    
    Args:
        timestamp: Datetime to check
        hours: Hours threshold
        
    Returns:
        True if within threshold
    """
    cutoff = datetime.now() - timedelta(hours=hours)
    return timestamp > cutoff

def sentiment_analysis(text: str) -> Dict[str, float]:
    """
    Perform basic sentiment analysis on text
    
    Args:
        text: Text to analyze
        
    Returns:
        Dictionary with polarity and subjectivity scores
    """
    if not text:
        return {'polarity': 0.0, 'subjectivity': 0.0}
    
    blob = TextBlob(text)
    return {
        'polarity': blob.sentiment.polarity,
        'subjectivity': blob.sentiment.subjectivity
    }

def format_number(num: int) -> str:
    """
    Format large numbers with K, M suffixes
    
    Args:
        num: Number to format
        
    Returns:
        Formatted string
    """
    if num >= 1000000:
        return f"{num/1000000:.1f}M"
    elif num >= 1000:
        return f"{num/1000:.1f}K"
    else:
        return str(num)
