"""
Quick start script for US Micro-Niche Trend Tracker
"""
import os
import sys
from datetime import datetime

def print_banner():
    """Print welcome banner"""
    print("=" * 70)
    print("🚀 US MICRO-NICHE TREND TRACKER - QUICK START")
    print("=" * 70)
    print("ABD pazarına odaklı mikro-niche tişört, hoodie ve mug trend takibi")
    print()

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_modules = [
        ('requests', 'requests'),
        ('pandas', 'pandas'),
        ('pytrends', 'pytrends'),
        ('schedule', 'schedule'),
        ('beautifulsoup4', 'bs4'),
        ('python-dotenv', 'dotenv')
    ]

    missing = []
    for display_name, import_name in required_modules:
        try:
            __import__(import_name)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name}")
            missing.append(display_name)
    
    if missing:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("   ✅ All dependencies installed!")
    return True

def check_api_keys():
    """Check API key configuration"""
    print("\n🔑 Checking API configuration...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    api_keys = {
        'ETSY_API_KEY': 'Etsy API',
        'TIKTOK_API_KEY': 'TikTok API',
        'SLACK_BOT_TOKEN': 'Slack Bot (Optional)',
        'DISCORD_BOT_TOKEN': 'Discord Bot (Optional)',
        'EMAIL_USERNAME': 'Email (Optional)'
    }
    
    configured = 0
    for key, name in api_keys.items():
        value = os.getenv(key)
        if value and value.strip():
            print(f"   ✅ {name}")
            configured += 1
        else:
            print(f"   ⚠️  {name} - Not configured")
    
    print(f"\n   📊 {configured}/{len(api_keys)} services configured")
    
    if configured == 0:
        print("\n💡 Note: System will work with Google Trends only (no API key required)")
        print("   For full functionality, configure API keys in .env file")
    
    return configured > 0

def run_quick_test():
    """Run a quick system test"""
    print("\n🧪 Running quick system test...")
    
    try:
        # Add src to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        
        from main import TrendTracker
        
        # Initialize tracker
        tracker = TrendTracker()
        
        # Run quick test
        test_results = tracker.test_system()
        
        print("   📋 Test Results:")
        
        # Collectors
        collectors = test_results.get('collectors', {})
        working_collectors = sum(1 for result in collectors.values() if result)
        print(f"   🔍 Data Collectors: {working_collectors}/{len(collectors)} working")
        
        for platform, result in collectors.items():
            status = "✅" if result else "❌"
            print(f"      {status} {platform.title()}")
        
        # Other components
        analyzer_ok = test_results.get('analyzer', False)
        reports_ok = test_results.get('report_generator', False)
        
        print(f"   📊 Trend Analyzer: {'✅' if analyzer_ok else '❌'}")
        print(f"   📄 Report Generator: {'✅' if reports_ok else '❌'}")
        
        # Notifications
        notifications = test_results.get('notifications', {})
        if notifications:
            working_notifications = sum(1 for result in notifications.values() if result)
            print(f"   🔔 Notifications: {working_notifications}/{len(notifications)} working")
        
        return working_collectors > 0 and analyzer_ok and reports_ok
        
    except Exception as e:
        print(f"   ❌ Test failed: {str(e)}")
        return False

def show_usage_examples():
    """Show usage examples"""
    print("\n📚 USAGE EXAMPLES:")
    print("-" * 50)
    
    examples = [
        ("System Test", "python main.py --mode test"),
        ("Single Analysis", "python main.py --mode run"),
        ("Custom Keywords", "python main.py --mode run --keywords \"funny cat shirt\" \"dog mom hoodie\""),
        ("Scheduled Mode", "python main.py --mode schedule"),
        ("Example Usage", "python example_usage.py")
    ]
    
    for name, command in examples:
        print(f"🔸 {name}:")
        print(f"   {command}")
        print()

def show_next_steps():
    """Show next steps"""
    print("🎯 NEXT STEPS:")
    print("-" * 50)
    
    steps = [
        "1. Configure API keys in .env file for full functionality",
        "2. Run: python main.py --mode test",
        "3. Try: python main.py --mode run",
        "4. Check generated reports in output/ folder",
        "5. Set up scheduled analysis: python main.py --mode schedule"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print("\n📖 For detailed documentation, see README.md")

def main():
    """Main quick start function"""
    print_banner()
    
    # Check dependencies
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ Please install dependencies first!")
        return
    
    # Check API configuration
    api_configured = check_api_keys()
    
    # Run quick test
    test_ok = run_quick_test()
    
    if test_ok:
        print("\n✅ SYSTEM READY!")
        print("   Your trend tracker is working and ready to use.")
    else:
        print("\n⚠️  SYSTEM PARTIALLY READY")
        print("   Some components may not work without API keys.")
    
    # Show usage examples
    show_usage_examples()
    
    # Show next steps
    show_next_steps()
    
    print("\n" + "=" * 70)
    print("🎉 Welcome to US Micro-Niche Trend Tracker!")
    print("   Happy trend hunting! 🔍📈")
    print("=" * 70)

if __name__ == "__main__":
    main()
