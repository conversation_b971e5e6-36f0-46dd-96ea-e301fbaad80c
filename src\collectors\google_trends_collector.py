"""
Google Trends data collector for search trend analysis
"""
from typing import Dict, List, Any, Optional
import time
from datetime import datetime, timedelta
from pytrends.request import TrendReq
from src.collectors.base_collector import BaseCollector
from config import GoogleTrendsConfig
from src.utils.helpers import clean_text, normalize_keyword

class GoogleTrendsCollector(BaseCollector):
    """
    Collector for Google Trends search data
    """
    
    def __init__(self):
        """
        Initialize Google Trends collector
        Note: Google Trends doesn't require API key for basic usage
        """
        super().__init__("google_trends", None, 2.0)  # 2 second rate limit
        self.pytrends = TrendReq(
            hl='en-US',
            tz=360,  # US timezone offset
            timeout=(10, 25),
            retries=2,
            backoff_factor=0.1
        )
        
    def _get_auth_headers(self) -> Dict[str, str]:
        """Google Trends doesn't require auth headers"""
        return {}
    
    def collect_data(self, keywords: List[str], **kwargs) -> Dict[str, Any]:
        """
        Collect Google Trends data for given keywords
        
        Args:
            keywords: List of keywords to search
            **kwargs: Additional parameters
            
        Returns:
            Dictionary containing Google Trends data
        """
        results = {
            'platform': 'google_trends',
            'timestamp': datetime.now().isoformat(),
            'keywords': {},
            'related_queries': {},
            'summary': {
                'total_keywords': len(keywords),
                'successful_searches': 0,
                'trending_keywords': []
            }
        }
        
        # Process keywords in batches (Google Trends API limitation)
        batch_size = 5
        for i in range(0, len(keywords), batch_size):
            batch = keywords[i:i + batch_size]
            
            self.logger.info(f"Processing Google Trends batch: {batch}")
            
            batch_data = self._get_trends_data(batch)
            if batch_data:
                for keyword in batch:
                    if keyword in batch_data:
                        results['keywords'][keyword] = batch_data[keyword]
                        results['summary']['successful_searches'] += 1
                
                # Get related queries for this batch
                related_data = self._get_related_queries(batch)
                if related_data:
                    results['related_queries'].update(related_data)
            
            time.sleep(2)  # Rate limiting between batches
        
        # Identify trending keywords
        results['summary']['trending_keywords'] = self._identify_trending_keywords(
            results['keywords']
        )
        
        return results
    
    def _get_trends_data(self, keywords: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get Google Trends data for a batch of keywords
        
        Args:
            keywords: List of keywords (max 5)
            
        Returns:
            Trends data for keywords
        """
        try:
            # Build payload for Google Trends
            self.pytrends.build_payload(
                keywords,
                cat=0,  # All categories
                timeframe=GoogleTrendsConfig.TIMEFRAME,
                geo=GoogleTrendsConfig.GEO,
                gprop=''  # Web search
            )
            
            # Get interest over time
            interest_over_time = self.pytrends.interest_over_time()
            
            if interest_over_time.empty:
                return None
            
            # Get interest by region
            interest_by_region = self.pytrends.interest_by_region(
                resolution='REGION',
                inc_low_vol=True,
                inc_geo_code=False
            )
            
            # Process the data
            results = {}
            for keyword in keywords:
                if keyword in interest_over_time.columns:
                    keyword_data = self._process_keyword_trends(
                        keyword,
                        interest_over_time,
                        interest_by_region
                    )
                    results[keyword] = keyword_data
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting trends data: {str(e)}")
            return None
    
    def _process_keyword_trends(self, keyword: str, interest_over_time, interest_by_region) -> Dict[str, Any]:
        """
        Process trends data for a specific keyword
        
        Args:
            keyword: The keyword
            interest_over_time: Time series data
            interest_by_region: Regional data
            
        Returns:
            Processed keyword trends
        """
        # Get time series data
        time_series = interest_over_time[keyword].tolist()
        dates = interest_over_time.index.strftime('%Y-%m-%d').tolist()
        
        # Calculate trend metrics
        current_interest = time_series[-1] if time_series else 0
        avg_interest = sum(time_series) / len(time_series) if time_series else 0
        max_interest = max(time_series) if time_series else 0
        
        # Calculate growth rate (last value vs average)
        growth_rate = 0
        if avg_interest > 0:
            growth_rate = ((current_interest - avg_interest) / avg_interest) * 100
        
        # Get top regions
        top_regions = []
        if keyword in interest_by_region.columns:
            region_data = interest_by_region[keyword].sort_values(ascending=False)
            top_regions = [
                {'region': region, 'interest': int(interest)}
                for region, interest in region_data.head(10).items()
                if interest > 0
            ]
        
        # Determine trend direction
        trend_direction = 'stable'
        if growth_rate > 20:
            trend_direction = 'rising'
        elif growth_rate < -20:
            trend_direction = 'falling'
        
        return {
            'keyword': keyword,
            'current_interest': current_interest,
            'avg_interest': round(avg_interest, 2),
            'max_interest': max_interest,
            'growth_rate': round(growth_rate, 2),
            'trend_direction': trend_direction,
            'time_series': list(zip(dates, time_series)),
            'top_regions': top_regions,
            'trend_score': self._calculate_trend_score(current_interest, growth_rate),
            'timestamp': datetime.now().isoformat()
        }
    
    def _get_related_queries(self, keywords: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get related queries for keywords
        
        Args:
            keywords: List of keywords
            
        Returns:
            Related queries data
        """
        try:
            related_queries = self.pytrends.related_queries()
            
            if not related_queries:
                return None
            
            results = {}
            for keyword in keywords:
                if keyword in related_queries:
                    keyword_related = related_queries[keyword]
                    
                    # Process top and rising queries
                    top_queries = []
                    rising_queries = []
                    
                    if keyword_related['top'] is not None:
                        top_queries = [
                            {'query': query, 'value': value}
                            for query, value in keyword_related['top'].head(10).values
                        ]
                    
                    if keyword_related['rising'] is not None:
                        rising_queries = [
                            {'query': query, 'value': value}
                            for query, value in keyword_related['rising'].head(10).values
                        ]
                    
                    results[keyword] = {
                        'top_queries': top_queries,
                        'rising_queries': rising_queries
                    }
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting related queries: {str(e)}")
            return None
    
    def _calculate_trend_score(self, current_interest: int, growth_rate: float) -> float:
        """
        Calculate trend score based on current interest and growth
        
        Args:
            current_interest: Current search interest (0-100)
            growth_rate: Growth rate percentage
            
        Returns:
            Trend score between 0 and 1
        """
        # Normalize current interest (0-100 to 0-1)
        interest_score = current_interest / 100
        
        # Normalize growth rate (-100 to +500 to 0-1)
        growth_score = max(0, min(1, (growth_rate + 100) / 600))
        
        # Weighted combination (favor growth over absolute interest)
        return (interest_score * 0.3) + (growth_score * 0.7)
    
    def _identify_trending_keywords(self, keywords_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Identify trending keywords based on growth rate and interest
        
        Args:
            keywords_data: Dictionary of keyword trend data
            
        Returns:
            List of trending keywords sorted by trend score
        """
        trending = []
        
        for keyword, data in keywords_data.items():
            if data['growth_rate'] > 10 and data['current_interest'] > 5:  # Thresholds
                trending.append({
                    'keyword': keyword,
                    'trend_score': data['trend_score'],
                    'growth_rate': data['growth_rate'],
                    'current_interest': data['current_interest']
                })
        
        # Sort by trend score
        return sorted(trending, key=lambda x: x['trend_score'], reverse=True)
    
    def get_trending_searches(self, category: str = None) -> Dict[str, Any]:
        """
        Get currently trending searches in the US
        
        Args:
            category: Optional category filter
            
        Returns:
            Trending searches data
        """
        try:
            # Get trending searches for US
            trending_searches = self.pytrends.trending_searches(pn='united_states')
            
            if trending_searches.empty:
                return {}
            
            # Filter for design/fashion related terms
            design_terms = ['design', 'custom', 'shirt', 'hoodie', 'mug', 'print', 'art']
            
            relevant_trends = []
            for search_term in trending_searches[0].head(20):  # Top 20 trending
                search_lower = search_term.lower()
                if any(term in search_lower for term in design_terms):
                    relevant_trends.append(search_term)
            
            return {
                'trending_searches': relevant_trends,
                'timestamp': datetime.now().isoformat(),
                'total_found': len(relevant_trends)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting trending searches: {str(e)}")
            return {}
