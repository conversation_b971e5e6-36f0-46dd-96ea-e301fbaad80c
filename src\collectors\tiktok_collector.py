"""
TikTok data collector for hashtag trends and viral content
"""
from typing import Dict, List, Any, Optional
import time
from datetime import datetime, timedelta
from src.collectors.base_collector import BaseCollector
from config import TikTokConfig
from src.utils.helpers import clean_text, normalize_keyword

class Tik<PERSON>okCollector(BaseCollector):
    """
    Collector for TikTok hashtag and video trend data
    """
    
    def __init__(self, api_key: str):
        """
        Initialize TikTok collector
        
        Args:
            api_key: TikTok API key
        """
        super().__init__("tiktok", api_key, 1.0)  # 1 second rate limit
        self.base_url = TikTokConfig.BASE_URL
        
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get TikTok API authentication headers"""
        return {
            'Authorization': f'Bearer {self.api_key}'
        }
    
    def collect_data(self, keywords: List[str], **kwargs) -> Dict[str, Any]:
        """
        Collect TikTok data for given keywords
        
        Args:
            keywords: List of keywords to search
            **kwargs: Additional parameters
            
        Returns:
            Dictionary containing TikTok trend data
        """
        results = {
            'platform': 'tiktok',
            'timestamp': datetime.now().isoformat(),
            'keywords': {},
            'hashtag_data': {},
            'summary': {
                'total_keywords': len(keywords),
                'successful_searches': 0,
                'total_videos': 0,
                'total_hashtag_views': 0
            }
        }
        
        # Collect hashtag data for predefined trending hashtags
        hashtag_data = self._collect_hashtag_trends()
        if hashtag_data:
            results['hashtag_data'] = hashtag_data
        
        # Search for videos with keywords
        for keyword in keywords:
            self.logger.info(f"Collecting TikTok data for keyword: {keyword}")
            
            keyword_data = self._search_videos(keyword)
            if keyword_data:
                results['keywords'][keyword] = keyword_data
                results['summary']['successful_searches'] += 1
                results['summary']['total_videos'] += len(keyword_data.get('videos', []))
            
            time.sleep(1)  # Rate limiting
        
        return results
    
    def _collect_hashtag_trends(self) -> Dict[str, Any]:
        """
        Collect trending hashtag data
        
        Returns:
            Hashtag trend data
        """
        hashtag_results = {}
        
        for hashtag in TikTokConfig.TARGET_HASHTAGS:
            self.logger.info(f"Collecting hashtag data for: #{hashtag}")
            
            hashtag_info = self._get_hashtag_info(hashtag)
            if hashtag_info:
                hashtag_results[hashtag] = hashtag_info
            
            time.sleep(1)  # Rate limiting
        
        return hashtag_results
    
    def _get_hashtag_info(self, hashtag: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific hashtag
        
        Args:
            hashtag: Hashtag name (without #)
            
        Returns:
            Hashtag information
        """
        url = f"{self.base_url}{TikTokConfig.HASHTAG_ENDPOINT}"
        
        params = {
            'hashtag_name': hashtag,
            'fields': [
                'hashtag_name',
                'video_count',
                'view_count',
                'publish_time'
            ]
        }
        
        response_data = self._make_request(url, params, method='POST')
        
        if not response_data or 'data' not in response_data:
            return None
        
        hashtag_data = response_data['data']
        
        return {
            'hashtag': hashtag,
            'video_count': hashtag_data.get('video_count', 0),
            'view_count': hashtag_data.get('view_count', 0),
            'publish_time': hashtag_data.get('publish_time'),
            'trend_score': self._calculate_hashtag_trend_score(hashtag_data),
            'timestamp': datetime.now().isoformat()
        }
    
    def _search_videos(self, keyword: str) -> Optional[Dict[str, Any]]:
        """
        Search for videos containing specific keywords
        
        Args:
            keyword: Search keyword
            
        Returns:
            Video search results
        """
        url = f"{self.base_url}{TikTokConfig.VIDEO_ENDPOINT}"
        
        # Calculate date range (last 7 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        params = {
            'query': {
                'and': [
                    {'operation': 'IN', 'field_name': 'keyword', 'field_values': [keyword]},
                    {'operation': 'EQ', 'field_name': 'region_code', 'field_values': ['US']}
                ]
            },
            'fields': [
                'id',
                'video_description',
                'create_time',
                'username',
                'like_count',
                'comment_count',
                'share_count',
                'view_count',
                'hashtag_names'
            ],
            'max_count': 100,
            'start_date': start_date.strftime('%Y%m%d'),
            'end_date': end_date.strftime('%Y%m%d')
        }
        
        response_data = self._make_request(url, params, method='POST')
        
        if not response_data or 'data' not in response_data:
            return None
        
        return self._process_video_results(response_data['data'], keyword)
    
    def _process_video_results(self, videos: List[Dict], keyword: str) -> Dict[str, Any]:
        """
        Process TikTok video search results
        
        Args:
            videos: List of video data
            keyword: Original search keyword
            
        Returns:
            Processed video results
        """
        if not videos:
            return {
                'keyword': keyword,
                'total_videos': 0,
                'videos': [],
                'avg_engagement': 0,
                'trending_hashtags': []
            }
        
        processed_videos = []
        total_engagement = 0
        all_hashtags = []
        
        for video in videos:
            engagement = (
                video.get('like_count', 0) +
                video.get('comment_count', 0) +
                video.get('share_count', 0)
            )
            
            processed_video = {
                'id': video.get('id'),
                'description': clean_text(video.get('video_description', '')),
                'username': video.get('username'),
                'create_time': video.get('create_time'),
                'like_count': video.get('like_count', 0),
                'comment_count': video.get('comment_count', 0),
                'share_count': video.get('share_count', 0),
                'view_count': video.get('view_count', 0),
                'engagement_score': engagement,
                'hashtags': video.get('hashtag_names', [])
            }
            
            processed_videos.append(processed_video)
            total_engagement += engagement
            all_hashtags.extend(processed_video['hashtags'])
        
        # Calculate average engagement
        avg_engagement = total_engagement / len(processed_videos) if processed_videos else 0
        
        # Find trending hashtags
        hashtag_counts = {}
        for hashtag in all_hashtags:
            if hashtag and len(hashtag) > 2:
                clean_hashtag = clean_text(hashtag)
                hashtag_counts[clean_hashtag] = hashtag_counts.get(clean_hashtag, 0) + 1
        
        trending_hashtags = sorted(hashtag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'keyword': keyword,
            'total_videos': len(processed_videos),
            'videos': processed_videos,
            'avg_engagement': int(avg_engagement),
            'trending_hashtags': [{'hashtag': tag, 'count': count} for tag, count in trending_hashtags],
            'search_timestamp': datetime.now().isoformat()
        }
    
    def _calculate_hashtag_trend_score(self, hashtag_data: Dict[str, Any]) -> float:
        """
        Calculate trend score for a hashtag
        
        Args:
            hashtag_data: Hashtag metrics
            
        Returns:
            Trend score between 0 and 1
        """
        video_count = hashtag_data.get('video_count', 0)
        view_count = hashtag_data.get('view_count', 0)
        
        # Normalize scores (these thresholds would be adjusted based on real data)
        video_score = min(1.0, video_count / 10000)  # 10k videos = max score
        view_score = min(1.0, view_count / 100000000)  # 100M views = max score
        
        # Weighted combination
        return (video_score * 0.4) + (view_score * 0.6)
    
    def get_viral_content(self, category: str = None) -> Dict[str, Any]:
        """
        Get currently viral content related to design/fashion
        
        Args:
            category: Optional category filter
            
        Returns:
            Viral content data
        """
        # This would search for videos with high engagement in recent timeframe
        design_keywords = ['design', 'custom', 'tshirt', 'hoodie', 'mug', 'print']
        
        viral_content = {}
        for keyword in design_keywords[:3]:  # Limit to avoid rate limits
            content = self._search_videos(keyword)
            if content and content.get('videos'):
                # Filter for high engagement videos
                viral_videos = [
                    video for video in content['videos']
                    if video['engagement_score'] > 1000  # Threshold for "viral"
                ]
                if viral_videos:
                    viral_content[keyword] = {
                        'videos': viral_videos[:5],  # Top 5 viral videos
                        'total_engagement': sum(v['engagement_score'] for v in viral_videos)
                    }
        
        return viral_content
