"""
Notification manager for sending alerts via multiple channels
"""
import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from typing import Dict, List, Any, Optional
from datetime import datetime

from src.utils.logger import setup_logger
from config import Config

class NotificationManager:
    """
    Manage notifications across multiple channels (<PERSON><PERSON>ck, Discord, Email)
    """
    
    def __init__(self):
        """Initialize notification manager"""
        self.logger = setup_logger("notification_manager")
        self.slack_client = None
        self.discord_client = None
        
        # Initialize clients if tokens are available
        self._init_slack_client()
        self._init_discord_client()
    
    def _init_slack_client(self):
        """Initialize Slack client if token is available"""
        if Config.SLACK_BOT_TOKEN:
            try:
                from slack_sdk import WebClient
                self.slack_client = WebClient(token=Config.SLACK_BOT_TOKEN)
                self.logger.info("Slack client initialized")
            except ImportError:
                self.logger.warning("slack-sdk not installed, Slack notifications disabled")
            except Exception as e:
                self.logger.error(f"Failed to initialize Slack client: {str(e)}")
    
    def _init_discord_client(self):
        """Initialize Discord client if token is available"""
        if Config.DISCORD_BOT_TOKEN:
            try:
                import discord
                # Discord client initialization would be more complex for a bot
                # For webhook-based notifications, we'll use requests
                self.logger.info("Discord webhook support enabled")
            except ImportError:
                self.logger.warning("discord.py not installed, Discord notifications disabled")
    
    def send_trend_alert(self, analysis_results: Dict[str, Any], report_files: Dict[str, str] = None) -> Dict[str, bool]:
        """
        Send trend analysis alerts to all configured channels
        
        Args:
            analysis_results: Trend analysis results
            report_files: Optional dictionary of generated report files
            
        Returns:
            Dictionary indicating success/failure for each channel
        """
        self.logger.info("Sending trend analysis alerts")
        
        results = {}
        
        # Prepare alert content
        alert_content = self._prepare_alert_content(analysis_results)
        
        # Send Slack notification
        if self.slack_client:
            results['slack'] = self._send_slack_alert(alert_content, report_files)
        
        # Send Discord notification
        if Config.DISCORD_BOT_TOKEN and Config.DISCORD_CHANNEL_ID:
            results['discord'] = self._send_discord_alert(alert_content)
        
        # Send email notification
        if Config.EMAIL_USERNAME and Config.EMAIL_RECIPIENTS:
            results['email'] = self._send_email_alert(alert_content, report_files)
        
        return results
    
    def _prepare_alert_content(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare alert content from analysis results
        
        Args:
            analysis_results: Analysis results
            
        Returns:
            Formatted alert content
        """
        trending_keywords = analysis_results.get('trending_keywords', [])
        micro_niches = analysis_results.get('micro_niches', [])
        
        # Get top items
        top_trending = trending_keywords[:5]
        top_niches = micro_niches[:3]
        
        content = {
            'title': '🔥 US Micro-Niche Trend Alert',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_keywords': analysis_results.get('total_keywords_analyzed', 0),
                'trending_count': len(trending_keywords),
                'niche_count': len(micro_niches)
            },
            'top_trending': top_trending,
            'top_niches': top_niches,
            'recommendations': analysis_results.get('recommendations', {}).get('immediate_opportunities', [])[:3]
        }
        
        return content
    
    def _send_slack_alert(self, content: Dict[str, Any], report_files: Dict[str, str] = None) -> bool:
        """Send alert to Slack"""
        try:
            # Create Slack message blocks
            blocks = self._create_slack_blocks(content)
            
            response = self.slack_client.chat_postMessage(
                channel=Config.SLACK_CHANNEL,
                text=content['title'],
                blocks=blocks
            )
            
            # Upload report files if available
            if report_files and response['ok']:
                for report_type, filepath in report_files.items():
                    if report_type in ['summary', 'trending_keywords']:  # Upload key reports
                        try:
                            self.slack_client.files_upload(
                                channels=Config.SLACK_CHANNEL,
                                file=filepath,
                                title=f"Trend Analysis - {report_type.title()}"
                            )
                        except Exception as e:
                            self.logger.warning(f"Failed to upload {report_type} to Slack: {str(e)}")
            
            self.logger.info("Slack alert sent successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send Slack alert: {str(e)}")
            return False
    
    def _create_slack_blocks(self, content: Dict[str, Any]) -> List[Dict]:
        """Create Slack message blocks"""
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": content['title']
                }
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Generated: {content['timestamp']}"
                    }
                ]
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Keywords Analyzed:*\n{content['summary']['total_keywords']}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Trending Keywords:*\n{content['summary']['trending_count']}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Micro-Niches:*\n{content['summary']['niche_count']}"
                    }
                ]
            }
        ]
        
        # Add trending keywords section
        if content['top_trending']:
            trending_text = ""
            for i, item in enumerate(content['top_trending'], 1):
                score = item.get('composite_score', 0)
                platforms = ', '.join(item.get('platforms', []))
                trending_text += f"{i}. *{item['keyword']}* (Score: {score:.3f}, Platforms: {platforms})\n"
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*🔥 Top Trending Keywords:*\n{trending_text}"
                }
            })
        
        # Add micro-niches section
        if content['top_niches']:
            niches_text = ""
            for i, item in enumerate(content['top_niches'], 1):
                score = item.get('opportunity_score', 0)
                potential = item.get('market_potential', 'unknown')
                niches_text += f"{i}. *{item['keyword']}* (Opportunity: {score:.3f}, Potential: {potential})\n"
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*💎 Micro-Niche Opportunities:*\n{niches_text}"
                }
            })
        
        return blocks
    
    def _send_discord_alert(self, content: Dict[str, Any]) -> bool:
        """Send alert to Discord via webhook"""
        try:
            import requests
            
            # Create Discord embed
            embed = {
                "title": content['title'],
                "color": 0xFF6B35,  # Orange color
                "timestamp": datetime.now().isoformat(),
                "fields": [
                    {
                        "name": "📊 Summary",
                        "value": f"Keywords: {content['summary']['total_keywords']}\nTrending: {content['summary']['trending_count']}\nNiches: {content['summary']['niche_count']}",
                        "inline": True
                    }
                ]
            }
            
            # Add trending keywords
            if content['top_trending']:
                trending_text = ""
                for i, item in enumerate(content['top_trending'][:3], 1):
                    score = item.get('composite_score', 0)
                    trending_text += f"{i}. **{item['keyword']}** ({score:.3f})\n"
                
                embed["fields"].append({
                    "name": "🔥 Top Trending",
                    "value": trending_text,
                    "inline": True
                })
            
            # Note: This would require a Discord webhook URL
            # For now, we'll log that Discord notification would be sent
            self.logger.info("Discord alert prepared (webhook URL needed for actual sending)")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send Discord alert: {str(e)}")
            return False
    
    def _send_email_alert(self, content: Dict[str, Any], report_files: Dict[str, str] = None) -> bool:
        """Send email alert"""
        try:
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = Config.EMAIL_USERNAME
            msg['To'] = ', '.join(Config.EMAIL_RECIPIENTS)
            msg['Subject'] = f"Trend Alert - {content['timestamp']}"
            
            # Create email body
            body = self._create_email_body(content)
            msg.attach(MIMEText(body, 'html'))
            
            # Attach report files if available
            if report_files:
                for report_type, filepath in report_files.items():
                    if report_type in ['summary', 'trending_keywords']:
                        try:
                            with open(filepath, 'rb') as f:
                                attachment = MIMEText(f.read(), 'base64', 'utf-8')
                                attachment.add_header(
                                    'Content-Disposition',
                                    f'attachment; filename="{os.path.basename(filepath)}"'
                                )
                                msg.attach(attachment)
                        except Exception as e:
                            self.logger.warning(f"Failed to attach {report_type}: {str(e)}")
            
            # Send email
            with smtplib.SMTP(Config.EMAIL_SMTP_SERVER, Config.EMAIL_SMTP_PORT) as server:
                server.starttls()
                server.login(Config.EMAIL_USERNAME, Config.EMAIL_PASSWORD)
                server.send_message(msg)
            
            self.logger.info("Email alert sent successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {str(e)}")
            return False
    
    def _create_email_body(self, content: Dict[str, Any]) -> str:
        """Create HTML email body"""
        html = f"""
        <html>
        <body>
            <h2>{content['title']}</h2>
            <p><strong>Generated:</strong> {content['timestamp']}</p>
            
            <h3>📊 Summary</h3>
            <ul>
                <li>Keywords Analyzed: {content['summary']['total_keywords']}</li>
                <li>Trending Keywords: {content['summary']['trending_count']}</li>
                <li>Micro-Niches: {content['summary']['niche_count']}</li>
            </ul>
        """
        
        # Add trending keywords
        if content['top_trending']:
            html += "<h3>🔥 Top Trending Keywords</h3><ol>"
            for item in content['top_trending']:
                score = item.get('composite_score', 0)
                platforms = ', '.join(item.get('platforms', []))
                html += f"<li><strong>{item['keyword']}</strong> (Score: {score:.3f}, Platforms: {platforms})</li>"
            html += "</ol>"
        
        # Add micro-niches
        if content['top_niches']:
            html += "<h3>💎 Micro-Niche Opportunities</h3><ol>"
            for item in content['top_niches']:
                score = item.get('opportunity_score', 0)
                potential = item.get('market_potential', 'unknown')
                html += f"<li><strong>{item['keyword']}</strong> (Opportunity: {score:.3f}, Potential: {potential})</li>"
            html += "</ol>"
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def send_test_notification(self, channel: str = 'all') -> Dict[str, bool]:
        """
        Send test notification to verify configuration
        
        Args:
            channel: Channel to test ('slack', 'discord', 'email', or 'all')
            
        Returns:
            Test results for each channel
        """
        test_content = {
            'title': '🧪 Test Notification - US Micro-Niche Trend Tracker',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_keywords': 100,
                'trending_count': 5,
                'niche_count': 3
            },
            'top_trending': [
                {'keyword': 'test-keyword', 'composite_score': 0.85, 'platforms': ['etsy', 'tiktok']}
            ],
            'top_niches': [
                {'keyword': 'test-niche', 'opportunity_score': 0.75, 'market_potential': 'high'}
            ]
        }
        
        results = {}
        
        if channel in ['slack', 'all'] and self.slack_client:
            results['slack'] = self._send_slack_alert(test_content)
        
        if channel in ['discord', 'all'] and Config.DISCORD_BOT_TOKEN:
            results['discord'] = self._send_discord_alert(test_content)
        
        if channel in ['email', 'all'] and Config.EMAIL_USERNAME:
            results['email'] = self._send_email_alert(test_content)
        
        return results
