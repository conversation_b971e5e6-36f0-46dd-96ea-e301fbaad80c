"""
Configuration settings for the US Micro-Niche Trend Tracker
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Main configuration class"""
    
    # API Keys
    ETSY_API_KEY = os.getenv('ETSY_API_KEY')
    TIKTOK_API_KEY = os.getenv('TIKTOK_API_KEY')
    GOOGLE_TRENDS_API_KEY = os.getenv('GOOGLE_TRENDS_API_KEY')
    
    # Notification settings
    SLACK_BOT_TOKEN = os.getenv('SLACK_BOT_TOKEN')
    SLACK_CHANNEL = os.getenv('SLACK_CHANNEL', '#trends')
    DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN')
    DISCORD_CHANNEL_ID = os.getenv('DISCORD_CHANNEL_ID')
    
    # Email settings
    EMAIL_SMTP_SERVER = os.getenv('EMAIL_SMTP_SERVER', 'smtp.gmail.com')
    EMAIL_SMTP_PORT = int(os.getenv('EMAIL_SMTP_PORT', '587'))
    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    EMAIL_RECIPIENTS = os.getenv('EMAIL_RECIPIENTS', '').split(',')
    
    # Target market
    TARGET_COUNTRY = 'US'
    TARGET_LANGUAGE = 'en'
    
    # Product categories
    PRODUCT_CATEGORIES = [
        't-shirt', 'tshirt', 'tee',
        'hoodie', 'sweatshirt',
        'mug', 'coffee mug', 'cup'
    ]
    
    # Trend analysis settings
    TREND_THRESHOLD = 0.7  # Minimum trend score (0-1)
    MIN_SEARCH_VOLUME = 100  # Minimum search volume
    ANALYSIS_PERIOD_HOURS = 24  # Hours to look back for trends
    TOP_TRENDS_COUNT = 20  # Number of top trends to report
    
    # Data collection settings
    MAX_ETSY_RESULTS = 500
    MAX_TIKTOK_RESULTS = 100
    SCRAPING_DELAY = 2  # Seconds between requests
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///trends.db')
    
    # Scheduling
    COLLECTION_INTERVAL_HOURS = 6  # How often to collect data
    REPORT_INTERVAL_HOURS = 24  # How often to generate reports
    
    # File paths
    OUTPUT_DIR = 'output'
    LOGS_DIR = 'logs'
    DATA_DIR = 'data'

class EtsyConfig:
    """Etsy-specific configuration"""
    BASE_URL = 'https://openapi.etsy.com/v3'
    SEARCH_ENDPOINT = '/application/listings/active'
    RATE_LIMIT = 10  # requests per second
    
    # Search parameters
    SEARCH_CATEGORIES = [
        'clothing/unisex_adult_clothing/tops_and_tees',
        'clothing/unisex_adult_clothing/sweaters',
        'home_and_living/kitchen_and_dining/drink_and_barware'
    ]

class TikTokConfig:
    """TikTok-specific configuration"""
    BASE_URL = 'https://open-api.tiktok.com'
    HASHTAG_ENDPOINT = '/v2/research/hashtag/info/'
    VIDEO_ENDPOINT = '/v2/research/video/query/'
    RATE_LIMIT = 100  # requests per day
    
    # Hashtag categories related to design/fashion
    TARGET_HASHTAGS = [
        'tshirtdesign', 'customtshirt', 'printedtshirt',
        'hoodiedesign', 'customhoodie',
        'mugdesign', 'custommug', 'coffeemug',
        'smallbusiness', 'handmade', 'etsy'
    ]

class GoogleTrendsConfig:
    """Google Trends configuration"""
    GEO = 'US'  # United States
    TIMEFRAME = 'now 7-d'  # Last 7 days
    CATEGORY = 0  # All categories
    
    # Keywords to track
    BASE_KEYWORDS = [
        'custom t-shirt', 'personalized hoodie', 'custom mug',
        'print on demand', 'tshirt design', 'hoodie design'
    ]
