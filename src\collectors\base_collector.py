"""
Base collector class for all data collection modules
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import time
import requests
from datetime import datetime
from src.utils.logger import setup_logger, log_api_call, log_error
from src.utils.helpers import rate_limit_delay

class BaseCollector(ABC):
    """
    Abstract base class for all data collectors
    """
    
    def __init__(self, name: str, api_key: Optional[str] = None, rate_limit: float = 1.0):
        """
        Initialize base collector
        
        Args:
            name: Collector name for logging
            api_key: API key if required
            rate_limit: Minimum seconds between API calls
        """
        self.name = name
        self.api_key = api_key
        self.rate_limit = rate_limit
        self.last_call_time = 0.0
        self.logger = setup_logger(f"collector_{name}")
        self.session = requests.Session()
        
        # Set common headers
        self.session.headers.update({
            'User-Agent': 'US-Micro-Niche-Trend-Tracker/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        
        if api_key:
            self.session.headers.update(self._get_auth_headers())
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for API requests
        
        Returns:
            Dictionary of headers
        """
        pass
    
    @abstractmethod
    def collect_data(self, keywords: List[str], **kwargs) -> Dict[str, Any]:
        """
        Collect data for given keywords
        
        Args:
            keywords: List of keywords to search for
            **kwargs: Additional parameters
            
        Returns:
            Collected data dictionary
        """
        pass
    
    def _make_request(self, url: str, params: Dict[str, Any] = None, 
                     method: str = 'GET') -> Optional[Dict[str, Any]]:
        """
        Make rate-limited API request
        
        Args:
            url: Request URL
            params: Request parameters
            method: HTTP method
            
        Returns:
            Response data or None if failed
        """
        # Apply rate limiting
        rate_limit_delay(self.last_call_time, self.rate_limit)
        self.last_call_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=params, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            
            log_api_call(self.logger, self.name, url, f"{response.status_code}")
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            log_error(self.logger, e, f"API request to {url}")
            return None
        except Exception as e:
            log_error(self.logger, e, f"Processing response from {url}")
            return None
    
    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """
        Extract potential keywords from text content
        
        Args:
            text: Text to analyze
            
        Returns:
            List of extracted keywords
        """
        from src.utils.helpers import extract_keywords
        return extract_keywords(text)
    
    def _calculate_engagement_score(self, metrics: Dict[str, int]) -> float:
        """
        Calculate engagement score from various metrics
        
        Args:
            metrics: Dictionary of engagement metrics
            
        Returns:
            Normalized engagement score (0-1)
        """
        # Default implementation - can be overridden by subclasses
        total_engagement = sum(metrics.values())
        
        # Normalize based on typical engagement ranges for the platform
        # This is a simplified calculation - real implementation would be more sophisticated
        if total_engagement > 10000:
            return 1.0
        elif total_engagement > 1000:
            return 0.8
        elif total_engagement > 100:
            return 0.6
        elif total_engagement > 10:
            return 0.4
        else:
            return 0.2
    
    def get_collection_summary(self) -> Dict[str, Any]:
        """
        Get summary of collection activity
        
        Returns:
            Summary dictionary
        """
        return {
            'collector': self.name,
            'last_collection': datetime.now().isoformat(),
            'status': 'active' if self.api_key else 'no_api_key'
        }
