"""
Example usage of the US Micro-Niche Trend Tracker
"""
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import TrendTracker

def example_single_run():
    """Example: Single trend analysis run"""
    print("=== Example: Single Trend Analysis ===\n")
    
    # Initialize tracker
    tracker = TrendTracker()
    
    # Custom keywords for analysis
    custom_keywords = [
        'funny cat shirt',
        'dog mom hoodie',
        'coffee addict mug',
        'vintage band tee',
        'sarcastic teacher shirt'
    ]
    
    print(f"Analyzing {len(custom_keywords)} custom keywords...")
    print("Keywords:", ", ".join(custom_keywords))
    print()
    
    # Run analysis
    results = tracker.run_analysis(custom_keywords)
    
    if results:
        print("✅ Analysis completed successfully!")
        print(f"📊 Results Summary:")
        print(f"   - Total keywords analyzed: {results.get('total_keywords_analyzed', 0)}")
        print(f"   - Trending keywords found: {len(results.get('trending_keywords', []))}")
        print(f"   - Micro-niches identified: {len(results.get('micro_niches', []))}")
        
        # Show top trending keywords
        trending = results.get('trending_keywords', [])
        if trending:
            print(f"\n🔥 Top 3 Trending Keywords:")
            for i, item in enumerate(trending[:3], 1):
                score = item.get('composite_score', 0)
                platforms = ', '.join(item.get('platforms', []))
                print(f"   {i}. {item['keyword']} (Score: {score:.3f}, Platforms: {platforms})")
        
        # Show micro-niches
        niches = results.get('micro_niches', [])
        if niches:
            print(f"\n💎 Top 3 Micro-Niche Opportunities:")
            for i, item in enumerate(niches[:3], 1):
                score = item.get('opportunity_score', 0)
                potential = item.get('market_potential', 'unknown')
                print(f"   {i}. {item['keyword']} (Opportunity: {score:.3f}, Potential: {potential})")
        
        # Show generated reports
        report_files = results.get('report_files', {})
        if report_files:
            print(f"\n📄 Generated Reports:")
            for report_type, filepath in report_files.items():
                print(f"   - {report_type.title()}: {os.path.basename(filepath)}")
    
    else:
        print("❌ Analysis failed. Check logs for details.")

def example_system_test():
    """Example: System component testing"""
    print("\n=== Example: System Component Test ===\n")
    
    tracker = TrendTracker()
    
    print("Testing all system components...")
    test_results = tracker.test_system()
    
    print("\n📋 Test Results:")
    
    # Test collectors
    print("🔍 Data Collectors:")
    for platform, result in test_results.get('collectors', {}).items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   - {platform.title()}: {status}")
    
    # Test analyzer
    analyzer_result = test_results.get('analyzer', False)
    status = "✅ PASS" if analyzer_result else "❌ FAIL"
    print(f"📊 Trend Analyzer: {status}")
    
    # Test report generator
    report_result = test_results.get('report_generator', False)
    status = "✅ PASS" if report_result else "❌ FAIL"
    print(f"📄 Report Generator: {status}")
    
    # Test notifications
    print("🔔 Notification Channels:")
    for channel, result in test_results.get('notifications', {}).items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   - {channel.title()}: {status}")

def example_custom_analysis():
    """Example: Custom analysis with specific focus"""
    print("\n=== Example: Custom Analysis - Pet-themed Products ===\n")
    
    tracker = TrendTracker()
    
    # Pet-themed keywords
    pet_keywords = [
        'dog dad shirt',
        'cat mom hoodie',
        'pet lover mug',
        'funny dog shirt',
        'crazy cat lady',
        'dog walker shirt',
        'pet groomer hoodie',
        'veterinarian mug',
        'puppy love shirt',
        'kitten shirt'
    ]
    
    print(f"Analyzing {len(pet_keywords)} pet-themed keywords...")
    
    results = tracker.run_analysis(pet_keywords)
    
    if results:
        print("✅ Pet-themed analysis completed!")
        
        # Filter for high-potential pet keywords
        trending = results.get('trending_keywords', [])
        pet_trends = [item for item in trending if item.get('market_potential') in ['high', 'medium']]
        
        if pet_trends:
            print(f"\n🐾 High-Potential Pet Keywords ({len(pet_trends)} found):")
            for item in pet_trends[:5]:
                keyword = item['keyword']
                score = item.get('composite_score', 0)
                potential = item.get('market_potential', 'unknown')
                
                # Get key metrics
                metrics = item.get('key_metrics', {})
                etsy_results = metrics.get('etsy_results', 'N/A')
                tiktok_engagement = metrics.get('tiktok_engagement', 'N/A')
                
                print(f"   • {keyword}")
                print(f"     Score: {score:.3f} | Potential: {potential}")
                print(f"     Etsy Results: {etsy_results} | TikTok Engagement: {tiktok_engagement}")
                print()
        
        # Show recommendations
        recommendations = results.get('recommendations', {})
        immediate_opps = recommendations.get('immediate_opportunities', [])
        
        if immediate_opps:
            print("💡 Immediate Opportunities:")
            for opp in immediate_opps[:3]:
                print(f"   • {opp['keyword']}: {opp['reason']}")
                print(f"     Suggested products: {', '.join(opp.get('suggested_products', []))}")
                print()

def example_data_collection_only():
    """Example: Data collection without analysis"""
    print("\n=== Example: Data Collection Only ===\n")
    
    tracker = TrendTracker()
    
    # Test keywords
    test_keywords = ['custom mug', 'funny hoodie']
    
    print(f"Collecting data for: {', '.join(test_keywords)}")
    
    # Collect data from all platforms
    collected_data = tracker.collect_all_data(test_keywords)
    
    print(f"\n📊 Data Collection Results:")
    print(f"Platforms collected from: {len(collected_data)}")
    
    for platform, data in collected_data.items():
        print(f"\n🔍 {platform.title()} Data:")
        
        if platform == 'etsy':
            keywords_data = data.get('keywords', {})
            print(f"   Keywords searched: {len(keywords_data)}")
            
            for keyword, keyword_data in keywords_data.items():
                results_count = keyword_data.get('total_results', 0)
                avg_price = keyword_data.get('avg_price', 0)
                print(f"   • {keyword}: {results_count} results, avg price ${avg_price:.2f}")
        
        elif platform == 'tiktok':
            keywords_data = data.get('keywords', {})
            hashtag_data = data.get('hashtag_data', {})
            print(f"   Keywords searched: {len(keywords_data)}")
            print(f"   Hashtags analyzed: {len(hashtag_data)}")
            
            for hashtag, hashtag_info in hashtag_data.items():
                video_count = hashtag_info.get('video_count', 0)
                view_count = hashtag_info.get('view_count', 0)
                print(f"   • #{hashtag}: {video_count} videos, {view_count:,} views")
        
        elif platform == 'google_trends':
            keywords_data = data.get('keywords', {})
            print(f"   Keywords analyzed: {len(keywords_data)}")
            
            for keyword, keyword_data in keywords_data.items():
                interest = keyword_data.get('current_interest', 0)
                growth = keyword_data.get('growth_rate', 0)
                direction = keyword_data.get('trend_direction', 'stable')
                print(f"   • {keyword}: {interest}% interest, {growth:+.1f}% growth ({direction})")

def main():
    """Run all examples"""
    print("🚀 US Micro-Niche Trend Tracker - Example Usage")
    print("=" * 60)
    
    try:
        # Example 1: Single analysis run
        example_single_run()
        
        # Example 2: System testing
        example_system_test()
        
        # Example 3: Custom pet-themed analysis
        example_custom_analysis()
        
        # Example 4: Data collection only
        example_data_collection_only()
        
        print("\n" + "=" * 60)
        print("✅ All examples completed successfully!")
        print("\nNext steps:")
        print("1. Configure your API keys in .env file")
        print("2. Run: python main.py --mode test")
        print("3. Run: python main.py --mode run")
        print("4. Set up scheduled runs: python main.py --mode schedule")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {str(e)}")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
