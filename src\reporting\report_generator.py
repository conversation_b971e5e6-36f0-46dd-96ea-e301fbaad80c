"""
Report generation module for trend analysis results
"""
import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd

from src.utils.logger import setup_logger
from src.utils.helpers import format_number
from config import Config

class ReportGenerator:
    """
    Generate various types of reports from trend analysis results
    """
    
    def __init__(self):
        """Initialize the report generator"""
        self.logger = setup_logger("report_generator")
        
        # Ensure output directory exists
        os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
        
    def generate_all_reports(self, analysis_results: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate all types of reports
        
        Args:
            analysis_results: Complete trend analysis results
            
        Returns:
            Dictionary with file paths of generated reports
        """
        self.logger.info("Generating all report types")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_files = {}
        
        # JSON report (complete data)
        json_file = self.generate_json_report(analysis_results, timestamp)
        if json_file:
            report_files['json'] = json_file
        
        # CSV reports
        csv_files = self.generate_csv_reports(analysis_results, timestamp)
        report_files.update(csv_files)
        
        # Summary report (human-readable)
        summary_file = self.generate_summary_report(analysis_results, timestamp)
        if summary_file:
            report_files['summary'] = summary_file
        
        # Excel report (comprehensive)
        excel_file = self.generate_excel_report(analysis_results, timestamp)
        if excel_file:
            report_files['excel'] = excel_file
        
        self.logger.info(f"Generated {len(report_files)} report files")
        return report_files
    
    def generate_json_report(self, analysis_results: Dict[str, Any], timestamp: str = None) -> Optional[str]:
        """
        Generate complete JSON report
        
        Args:
            analysis_results: Analysis results
            timestamp: Optional timestamp for filename
            
        Returns:
            Path to generated JSON file
        """
        try:
            if not timestamp:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            filename = f"trend_analysis_{timestamp}.json"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)
            
            # Add metadata
            report_data = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'version': '1.0',
                    'analysis_period_hours': Config.ANALYSIS_PERIOD_HOURS,
                    'trend_threshold': Config.TREND_THRESHOLD
                },
                'analysis_results': analysis_results
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Generated JSON report: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error generating JSON report: {str(e)}")
            return None
    
    def generate_csv_reports(self, analysis_results: Dict[str, Any], timestamp: str = None) -> Dict[str, str]:
        """
        Generate CSV reports for different data types
        
        Args:
            analysis_results: Analysis results
            timestamp: Optional timestamp for filename
            
        Returns:
            Dictionary with CSV file paths
        """
        if not timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        csv_files = {}
        
        # Trending keywords CSV
        trending_file = self._generate_trending_keywords_csv(
            analysis_results.get('trending_keywords', []), timestamp
        )
        if trending_file:
            csv_files['trending_keywords'] = trending_file
        
        # Micro-niches CSV
        niches_file = self._generate_micro_niches_csv(
            analysis_results.get('micro_niches', []), timestamp
        )
        if niches_file:
            csv_files['micro_niches'] = niches_file
        
        # Platform insights CSV
        insights_file = self._generate_platform_insights_csv(
            analysis_results.get('platform_insights', {}), timestamp
        )
        if insights_file:
            csv_files['platform_insights'] = insights_file
        
        return csv_files
    
    def _generate_trending_keywords_csv(self, trending_keywords: List[Dict], timestamp: str) -> Optional[str]:
        """Generate CSV for trending keywords"""
        try:
            filename = f"trending_keywords_{timestamp}.csv"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                if not trending_keywords:
                    f.write("No trending keywords found\n")
                    return filepath
                
                fieldnames = [
                    'keyword', 'composite_score', 'trend_strength', 'market_potential',
                    'platforms', 'etsy_results', 'etsy_avg_price', 'tiktok_videos',
                    'tiktok_engagement', 'google_interest', 'google_growth'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for item in trending_keywords:
                    key_metrics = item.get('key_metrics', {})
                    row = {
                        'keyword': item.get('keyword', ''),
                        'composite_score': round(item.get('composite_score', 0), 3),
                        'trend_strength': item.get('trend_strength', ''),
                        'market_potential': item.get('market_potential', ''),
                        'platforms': ', '.join(item.get('platforms', [])),
                        'etsy_results': key_metrics.get('etsy_results', ''),
                        'etsy_avg_price': key_metrics.get('etsy_avg_price', ''),
                        'tiktok_videos': key_metrics.get('tiktok_videos', ''),
                        'tiktok_engagement': key_metrics.get('tiktok_engagement', ''),
                        'google_interest': key_metrics.get('google_interest', ''),
                        'google_growth': key_metrics.get('google_growth', '')
                    }
                    writer.writerow(row)
            
            self.logger.info(f"Generated trending keywords CSV: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error generating trending keywords CSV: {str(e)}")
            return None
    
    def _generate_micro_niches_csv(self, micro_niches: List[Dict], timestamp: str) -> Optional[str]:
        """Generate CSV for micro-niches"""
        try:
            filename = f"micro_niches_{timestamp}.csv"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                if not micro_niches:
                    f.write("No micro-niches identified\n")
                    return filepath
                
                fieldnames = [
                    'keyword', 'opportunity_score', 'trend_strength', 'market_potential',
                    'composite_score', 'platforms'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for item in micro_niches:
                    row = {
                        'keyword': item.get('keyword', ''),
                        'opportunity_score': round(item.get('opportunity_score', 0), 3),
                        'trend_strength': item.get('trend_strength', ''),
                        'market_potential': item.get('market_potential', ''),
                        'composite_score': round(item.get('composite_score', 0), 3),
                        'platforms': ', '.join(item.get('platforms', []))
                    }
                    writer.writerow(row)
            
            self.logger.info(f"Generated micro-niches CSV: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error generating micro-niches CSV: {str(e)}")
            return None
    
    def _generate_platform_insights_csv(self, platform_insights: Dict, timestamp: str) -> Optional[str]:
        """Generate CSV for platform insights"""
        try:
            filename = f"platform_insights_{timestamp}.csv"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Platform', 'Metric', 'Value'])
                
                for platform, insights in platform_insights.items():
                    if isinstance(insights, dict):
                        for metric, value in insights.items():
                            if isinstance(value, (list, dict)):
                                value = str(value)
                            writer.writerow([platform, metric, value])
            
            self.logger.info(f"Generated platform insights CSV: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error generating platform insights CSV: {str(e)}")
            return None
    
    def generate_summary_report(self, analysis_results: Dict[str, Any], timestamp: str = None) -> Optional[str]:
        """
        Generate human-readable summary report
        
        Args:
            analysis_results: Analysis results
            timestamp: Optional timestamp for filename
            
        Returns:
            Path to generated summary file
        """
        try:
            if not timestamp:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            filename = f"trend_summary_{timestamp}.txt"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                self._write_summary_content(f, analysis_results)
            
            self.logger.info(f"Generated summary report: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error generating summary report: {str(e)}")
            return None

    def _write_summary_content(self, file, analysis_results: Dict[str, Any]):
        """Write summary content to file"""
        file.write("=" * 80 + "\n")
        file.write("US MICRO-NICHE TREND ANALYSIS SUMMARY\n")
        file.write("=" * 80 + "\n\n")

        # Analysis metadata
        file.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(f"Analysis Period: {Config.ANALYSIS_PERIOD_HOURS} hours\n")
        file.write(f"Total Keywords Analyzed: {analysis_results.get('total_keywords_analyzed', 0)}\n\n")

        # Top trending keywords
        trending = analysis_results.get('trending_keywords', [])
        file.write("TOP TRENDING KEYWORDS\n")
        file.write("-" * 40 + "\n")

        if trending:
            for i, item in enumerate(trending[:10], 1):
                file.write(f"{i:2d}. {item['keyword']}\n")
                file.write(f"    Score: {item.get('composite_score', 0):.3f} | ")
                file.write(f"Strength: {item.get('trend_strength', 'unknown')} | ")
                file.write(f"Potential: {item.get('market_potential', 'unknown')}\n")
                file.write(f"    Platforms: {', '.join(item.get('platforms', []))}\n\n")
        else:
            file.write("No trending keywords found above threshold.\n\n")

        # Micro-niches
        niches = analysis_results.get('micro_niches', [])
        file.write("MICRO-NICHE OPPORTUNITIES\n")
        file.write("-" * 40 + "\n")

        if niches:
            for i, item in enumerate(niches[:5], 1):
                file.write(f"{i}. {item['keyword']}\n")
                file.write(f"   Opportunity Score: {item['opportunity_score']:.3f}\n")
                file.write(f"   Market Potential: {item['market_potential']}\n\n")
        else:
            file.write("No micro-niche opportunities identified.\n\n")

        # Recommendations
        recommendations = analysis_results.get('recommendations', {})
        file.write("RECOMMENDATIONS\n")
        file.write("-" * 40 + "\n")

        # Immediate opportunities
        immediate = recommendations.get('immediate_opportunities', [])
        if immediate:
            file.write("Immediate Opportunities:\n")
            for opp in immediate[:3]:
                file.write(f"• {opp['keyword']}: {opp['reason']}\n")
            file.write("\n")

        # Design suggestions
        design_suggestions = recommendations.get('design_suggestions', [])
        if design_suggestions:
            file.write("Design Themes:\n")
            for suggestion in design_suggestions:
                file.write(f"• {suggestion}\n")
            file.write("\n")

        # Platform focus
        platform_focus = recommendations.get('platform_focus', {})
        if platform_focus:
            file.write("Platform Performance:\n")
            for platform, performance in platform_focus.items():
                file.write(f"• {platform.title()}: {performance}\n")
            file.write("\n")

    def generate_excel_report(self, analysis_results: Dict[str, Any], timestamp: str = None) -> Optional[str]:
        """
        Generate comprehensive Excel report with multiple sheets

        Args:
            analysis_results: Analysis results
            timestamp: Optional timestamp for filename

        Returns:
            Path to generated Excel file
        """
        try:
            if not timestamp:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            filename = f"trend_analysis_{timestamp}.xlsx"
            filepath = os.path.join(Config.OUTPUT_DIR, filename)

            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Trending keywords sheet
                trending_df = self._create_trending_keywords_dataframe(
                    analysis_results.get('trending_keywords', [])
                )
                trending_df.to_excel(writer, sheet_name='Trending Keywords', index=False)

                # Micro-niches sheet
                niches_df = self._create_micro_niches_dataframe(
                    analysis_results.get('micro_niches', [])
                )
                niches_df.to_excel(writer, sheet_name='Micro Niches', index=False)

                # Summary sheet
                summary_df = self._create_summary_dataframe(analysis_results)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

            self.logger.info(f"Generated Excel report: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Error generating Excel report: {str(e)}")
            return None

    def _create_trending_keywords_dataframe(self, trending_keywords: List[Dict]) -> pd.DataFrame:
        """Create DataFrame for trending keywords"""
        if not trending_keywords:
            return pd.DataFrame({'Message': ['No trending keywords found']})

        data = []
        for item in trending_keywords:
            key_metrics = item.get('key_metrics', {})
            data.append({
                'Keyword': item.get('keyword', ''),
                'Composite Score': round(item.get('composite_score', 0), 3),
                'Trend Strength': item.get('trend_strength', ''),
                'Market Potential': item.get('market_potential', ''),
                'Platforms': ', '.join(item.get('platforms', [])),
                'Etsy Results': key_metrics.get('etsy_results', ''),
                'Etsy Avg Price': key_metrics.get('etsy_avg_price', ''),
                'TikTok Videos': key_metrics.get('tiktok_videos', ''),
                'TikTok Engagement': key_metrics.get('tiktok_engagement', ''),
                'Google Interest': key_metrics.get('google_interest', ''),
                'Google Growth %': key_metrics.get('google_growth', '')
            })

        return pd.DataFrame(data)

    def _create_micro_niches_dataframe(self, micro_niches: List[Dict]) -> pd.DataFrame:
        """Create DataFrame for micro-niches"""
        if not micro_niches:
            return pd.DataFrame({'Message': ['No micro-niches identified']})

        data = []
        for item in micro_niches:
            data.append({
                'Keyword': item.get('keyword', ''),
                'Opportunity Score': round(item.get('opportunity_score', 0), 3),
                'Trend Strength': item.get('trend_strength', ''),
                'Market Potential': item.get('market_potential', ''),
                'Composite Score': round(item.get('composite_score', 0), 3),
                'Platforms': ', '.join(item.get('platforms', []))
            })

        return pd.DataFrame(data)

    def _create_summary_dataframe(self, analysis_results: Dict[str, Any]) -> pd.DataFrame:
        """Create summary DataFrame"""
        summary_data = [
            ['Analysis Date', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Analysis Period (hours)', Config.ANALYSIS_PERIOD_HOURS],
            ['Total Keywords Analyzed', analysis_results.get('total_keywords_analyzed', 0)],
            ['Trending Keywords Found', len(analysis_results.get('trending_keywords', []))],
            ['Micro-Niches Identified', len(analysis_results.get('micro_niches', []))],
            ['Trend Threshold', Config.TREND_THRESHOLD]
        ]

        return pd.DataFrame(summary_data, columns=['Metric', 'Value'])

    def get_report_summary(self, report_files: Dict[str, str]) -> str:
        """
        Generate a summary of generated reports

        Args:
            report_files: Dictionary of report file paths

        Returns:
            Summary string
        """
        summary = f"Generated {len(report_files)} report files:\n"

        for report_type, filepath in report_files.items():
            file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
            summary += f"• {report_type.title()}: {os.path.basename(filepath)} ({file_size:,} bytes)\n"

        return summary
