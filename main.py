"""
Main application for US Micro-Niche Trend Tracker
"""
import asyncio
import schedule
import time
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.collectors.etsy_collector import <PERSON>tsyCollector
from src.collectors.tiktok_collector import <PERSON><PERSON><PERSON>okCollector
from src.collectors.google_trends_collector import GoogleTrendsCollector
from src.analyzer.trend_analyzer import TrendAnalyzer
from src.reporting.report_generator import ReportGenerator
from src.notifications.notification_manager import NotificationManager
from src.utils.logger import setup_logger
from config import Config

class TrendTracker:
    """
    Main trend tracking application
    """
    
    def __init__(self):
        """Initialize the trend tracker"""
        self.logger = setup_logger("trend_tracker")
        
        # Initialize components
        self.collectors = {}
        self.analyzer = TrendAnalyzer()
        self.report_generator = ReportGenerator()
        self.notification_manager = NotificationManager()
        
        # Initialize collectors
        self._init_collectors()
        
        # Base keywords to track
        self.base_keywords = [
            'custom t-shirt', 'personalized hoodie', 'custom mug',
            'funny t-shirt', 'mom shirt', 'dad hoodie',
            'coffee mug design', 'vintage tee', 'sarcastic shirt',
            'family matching shirts', 'pet lover shirt', 'hobby shirt'
        ]
        
        self.logger.info("Trend Tracker initialized successfully")
    
    def _init_collectors(self):
        """Initialize data collectors"""
        # Etsy collector
        if Config.ETSY_API_KEY:
            self.collectors['etsy'] = EtsyCollector(Config.ETSY_API_KEY)
            self.logger.info("Etsy collector initialized")
        else:
            self.logger.warning("Etsy API key not found, Etsy collection disabled")
        
        # TikTok collector
        if Config.TIKTOK_API_KEY:
            self.collectors['tiktok'] = TikTokCollector(Config.TIKTOK_API_KEY)
            self.logger.info("TikTok collector initialized")
        else:
            self.logger.warning("TikTok API key not found, TikTok collection disabled")
        
        # Google Trends collector (no API key required)
        self.collectors['google_trends'] = GoogleTrendsCollector()
        self.logger.info("Google Trends collector initialized")
    
    def collect_all_data(self, keywords: List[str] = None) -> Dict[str, Any]:
        """
        Collect data from all available platforms
        
        Args:
            keywords: List of keywords to search for
            
        Returns:
            Collected data from all platforms
        """
        if keywords is None:
            keywords = self.base_keywords
        
        self.logger.info(f"Starting data collection for {len(keywords)} keywords")
        
        collected_data = {}
        
        # Collect from each platform
        for platform, collector in self.collectors.items():
            try:
                self.logger.info(f"Collecting data from {platform}")
                platform_data = collector.collect_data(keywords)
                
                if platform_data:
                    collected_data[platform] = platform_data
                    self.logger.info(f"Successfully collected {platform} data")
                else:
                    self.logger.warning(f"No data collected from {platform}")
                    
            except Exception as e:
                self.logger.error(f"Error collecting data from {platform}: {str(e)}")
        
        self.logger.info(f"Data collection complete. Collected from {len(collected_data)} platforms")
        return collected_data
    
    def run_analysis(self, keywords: List[str] = None) -> Dict[str, Any]:
        """
        Run complete trend analysis
        
        Args:
            keywords: Optional list of keywords to analyze
            
        Returns:
            Complete analysis results
        """
        self.logger.info("Starting trend analysis run")
        
        try:
            # Collect data
            collected_data = self.collect_all_data(keywords)
            
            if not collected_data:
                self.logger.error("No data collected, cannot perform analysis")
                return {}
            
            # Analyze trends
            self.logger.info("Analyzing trends")
            analysis_results = self.analyzer.analyze_trends(collected_data)
            
            # Generate reports
            self.logger.info("Generating reports")
            report_files = self.report_generator.generate_all_reports(analysis_results)
            
            # Send notifications
            if analysis_results.get('trending_keywords'):
                self.logger.info("Sending notifications")
                notification_results = self.notification_manager.send_trend_alert(
                    analysis_results, report_files
                )
                analysis_results['notification_results'] = notification_results
            
            # Add report file info to results
            analysis_results['report_files'] = report_files
            
            self.logger.info("Trend analysis run completed successfully")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"Error during trend analysis: {str(e)}")
            return {}
    
    def run_scheduled_analysis(self):
        """Run analysis as scheduled job"""
        self.logger.info("Running scheduled trend analysis")
        
        results = self.run_analysis()
        
        if results:
            trending_count = len(results.get('trending_keywords', []))
            niches_count = len(results.get('micro_niches', []))
            
            self.logger.info(
                f"Scheduled analysis complete: {trending_count} trending keywords, "
                f"{niches_count} micro-niches identified"
            )
        else:
            self.logger.error("Scheduled analysis failed")
    
    def start_scheduler(self):
        """Start the scheduled data collection and analysis"""
        self.logger.info("Starting trend tracker scheduler")
        
        # Schedule data collection every 6 hours
        schedule.every(Config.COLLECTION_INTERVAL_HOURS).hours.do(self.run_scheduled_analysis)
        
        # Schedule daily reports at 9 AM
        schedule.every().day.at("09:00").do(self.run_scheduled_analysis)
        
        self.logger.info(
            f"Scheduler configured: Collection every {Config.COLLECTION_INTERVAL_HOURS} hours, "
            f"Reports every {Config.REPORT_INTERVAL_HOURS} hours"
        )
        
        # Run initial analysis
        self.logger.info("Running initial analysis")
        self.run_scheduled_analysis()
        
        # Keep scheduler running
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            self.logger.info("Scheduler stopped by user")
        except Exception as e:
            self.logger.error(f"Scheduler error: {str(e)}")
    
    def test_system(self) -> Dict[str, Any]:
        """
        Test all system components
        
        Returns:
            Test results
        """
        self.logger.info("Running system tests")
        
        test_results = {
            'collectors': {},
            'analyzer': False,
            'report_generator': False,
            'notifications': {}
        }
        
        # Test collectors
        test_keywords = ['test keyword']
        for platform, collector in self.collectors.items():
            try:
                # Test with minimal data
                result = collector.get_collection_summary()
                test_results['collectors'][platform] = bool(result)
            except Exception as e:
                self.logger.error(f"Collector test failed for {platform}: {str(e)}")
                test_results['collectors'][platform] = False
        
        # Test analyzer
        try:
            # Create minimal test data
            test_data = {
                'google_trends': {
                    'keywords': {
                        'test': {
                            'current_interest': 50,
                            'growth_rate': 10,
                            'trend_score': 0.6
                        }
                    }
                }
            }
            analysis = self.analyzer.analyze_trends(test_data)
            test_results['analyzer'] = bool(analysis)
        except Exception as e:
            self.logger.error(f"Analyzer test failed: {str(e)}")
        
        # Test report generator
        try:
            test_analysis = {
                'trending_keywords': [
                    {'keyword': 'test', 'composite_score': 0.8, 'platforms': ['test']}
                ],
                'micro_niches': []
            }
            reports = self.report_generator.generate_all_reports(test_analysis)
            test_results['report_generator'] = bool(reports)
        except Exception as e:
            self.logger.error(f"Report generator test failed: {str(e)}")
        
        # Test notifications
        try:
            notification_tests = self.notification_manager.send_test_notification()
            test_results['notifications'] = notification_tests
        except Exception as e:
            self.logger.error(f"Notification test failed: {str(e)}")
        
        self.logger.info(f"System tests completed: {test_results}")
        return test_results

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='US Micro-Niche Trend Tracker')
    parser.add_argument('--mode', choices=['run', 'schedule', 'test'], default='run',
                       help='Run mode: single run, scheduled, or test')
    parser.add_argument('--keywords', nargs='+', 
                       help='Custom keywords to analyze')
    
    args = parser.parse_args()
    
    # Initialize tracker
    tracker = TrendTracker()
    
    if args.mode == 'test':
        # Run system tests
        print("Running system tests...")
        results = tracker.test_system()
        
        print("\n=== Test Results ===")
        for component, result in results.items():
            if isinstance(result, dict):
                print(f"{component.title()}:")
                for sub_component, sub_result in result.items():
                    status = "✅ PASS" if sub_result else "❌ FAIL"
                    print(f"  {sub_component}: {status}")
            else:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{component.title()}: {status}")
    
    elif args.mode == 'schedule':
        # Start scheduler
        print("Starting scheduled trend tracking...")
        tracker.start_scheduler()
    
    else:
        # Single run
        print("Running single trend analysis...")
        results = tracker.run_analysis(args.keywords)
        
        if results:
            trending_count = len(results.get('trending_keywords', []))
            niches_count = len(results.get('micro_niches', []))
            
            print(f"\n=== Analysis Complete ===")
            print(f"Trending Keywords: {trending_count}")
            print(f"Micro-Niches: {niches_count}")
            
            if results.get('report_files'):
                print(f"\nReports generated:")
                for report_type, filepath in results['report_files'].items():
                    print(f"  {report_type}: {filepath}")
        else:
            print("Analysis failed. Check logs for details.")

if __name__ == "__main__":
    main()
