"""
Logging utility for the trend tracker
"""
import logging
import os
from datetime import datetime
from config import Config

def setup_logger(name: str, level: str = 'INFO') -> logging.Logger:
    """
    Set up a logger with file and console handlers
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
    
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    os.makedirs(Config.LOGS_DIR, exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Prevent duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s - %(name)s - %(message)s'
    )
    
    # File handler
    log_file = os.path.join(
        Config.LOGS_DIR, 
        f"{name}_{datetime.now().strftime('%Y%m%d')}.log"
    )
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_api_call(logger: logging.Logger, platform: str, endpoint: str, status: str):
    """Log API call details"""
    logger.info(f"API Call - Platform: {platform}, Endpoint: {endpoint}, Status: {status}")

def log_trend_analysis(logger: logging.Logger, keyword: str, score: float, platforms: list):
    """Log trend analysis results"""
    logger.info(f"Trend Analysis - Keyword: {keyword}, Score: {score:.3f}, Platforms: {platforms}")

def log_error(logger: logging.Logger, error: Exception, context: str = ""):
    """Log error with context"""
    logger.error(f"Error in {context}: {str(error)}", exc_info=True)
