"""
Etsy data collector for product trends and search data
"""
from typing import Dict, List, Any, Optional
import time
from datetime import datetime
from src.collectors.base_collector import BaseCollector
from config import EtsyConfig
from src.utils.helpers import clean_text, normalize_keyword

class EtsyCollector(BaseCollector):
    """
    Collector for Etsy marketplace data
    """
    
    def __init__(self, api_key: str):
        """
        Initialize Etsy collector
        
        Args:
            api_key: Etsy API key
        """
        super().__init__("etsy", api_key, EtsyConfig.RATE_LIMIT)
        self.base_url = EtsyConfig.BASE_URL
        
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get Etsy API authentication headers"""
        return {
            'x-api-key': self.api_key
        }
    
    def collect_data(self, keywords: List[str], **kwargs) -> Dict[str, Any]:
        """
        Collect Etsy data for given keywords
        
        Args:
            keywords: List of keywords to search
            **kwargs: Additional parameters
            
        Returns:
            Dictionary containing Etsy trend data
        """
        results = {
            'platform': 'etsy',
            'timestamp': datetime.now().isoformat(),
            'keywords': {},
            'summary': {
                'total_keywords': len(keywords),
                'successful_searches': 0,
                'total_listings': 0
            }
        }
        
        for keyword in keywords:
            self.logger.info(f"Collecting Etsy data for keyword: {keyword}")
            
            keyword_data = self._search_listings(keyword)
            if keyword_data:
                results['keywords'][keyword] = keyword_data
                results['summary']['successful_searches'] += 1
                results['summary']['total_listings'] += keyword_data.get('total_results', 0)
            
            # Small delay between keyword searches
            time.sleep(0.5)
        
        return results
    
    def _search_listings(self, keyword: str) -> Optional[Dict[str, Any]]:
        """
        Search Etsy listings for a specific keyword
        
        Args:
            keyword: Search keyword
            
        Returns:
            Search results data
        """
        # Normalize keyword for search
        search_term = normalize_keyword(keyword)
        
        # Build search parameters
        params = {
            'keywords': search_term,
            'limit': 100,  # Max results per request
            'includes': 'Images,Shop,User',
            'sort_on': 'relevancy',
            'min_price': 5.0,  # Filter out very cheap items
            'ship_to': 'US'
        }
        
        # Add category filters for relevant products
        for category in EtsyConfig.SEARCH_CATEGORIES:
            if any(cat_keyword in search_term for cat_keyword in ['shirt', 'tee', 'hoodie', 'mug']):
                params['category'] = category
                break
        
        url = f"{self.base_url}{EtsyConfig.SEARCH_ENDPOINT}"
        response_data = self._make_request(url, params)
        
        if not response_data:
            return None
        
        return self._process_search_results(response_data, keyword)
    
    def _process_search_results(self, data: Dict[str, Any], keyword: str) -> Dict[str, Any]:
        """
        Process Etsy search results
        
        Args:
            data: Raw API response
            keyword: Original search keyword
            
        Returns:
            Processed results
        """
        listings = data.get('results', [])
        
        if not listings:
            return {
                'keyword': keyword,
                'total_results': 0,
                'listings': [],
                'avg_price': 0,
                'avg_views': 0,
                'trending_tags': []
            }
        
        # Process individual listings
        processed_listings = []
        total_price = 0
        total_views = 0
        all_tags = []
        
        for listing in listings[:50]:  # Limit processing to top 50 results
            processed_listing = {
                'listing_id': listing.get('listing_id'),
                'title': clean_text(listing.get('title', '')),
                'price': float(listing.get('price', {}).get('amount', 0)) / 100,  # Convert cents to dollars
                'views': listing.get('views', 0),
                'num_favorers': listing.get('num_favorers', 0),
                'tags': listing.get('tags', []),
                'shop_name': listing.get('shop', {}).get('shop_name', ''),
                'creation_date': listing.get('creation_timestamp'),
                'url': listing.get('url', '')
            }
            
            processed_listings.append(processed_listing)
            total_price += processed_listing['price']
            total_views += processed_listing['views']
            all_tags.extend(processed_listing['tags'])
        
        # Calculate averages
        num_listings = len(processed_listings)
        avg_price = total_price / num_listings if num_listings > 0 else 0
        avg_views = total_views / num_listings if num_listings > 0 else 0
        
        # Find trending tags
        tag_counts = {}
        for tag in all_tags:
            if tag and len(tag) > 2:  # Filter out very short tags
                clean_tag = clean_text(tag)
                tag_counts[clean_tag] = tag_counts.get(clean_tag, 0) + 1
        
        # Get top 10 most common tags
        trending_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'keyword': keyword,
            'total_results': data.get('count', 0),
            'listings': processed_listings,
            'avg_price': round(avg_price, 2),
            'avg_views': int(avg_views),
            'trending_tags': [{'tag': tag, 'count': count} for tag, count in trending_tags],
            'search_timestamp': datetime.now().isoformat()
        }
    
    def get_trending_products(self, category: str = None) -> Dict[str, Any]:
        """
        Get currently trending products on Etsy
        
        Args:
            category: Optional category filter
            
        Returns:
            Trending products data
        """
        params = {
            'limit': 50,
            'sort_on': 'hotness',  # Etsy's trending algorithm
            'includes': 'Images,Shop',
            'ship_to': 'US'
        }
        
        if category:
            params['category'] = category
        
        url = f"{self.base_url}{EtsyConfig.SEARCH_ENDPOINT}"
        response_data = self._make_request(url, params)
        
        if response_data:
            return self._process_search_results(response_data, "trending")
        
        return {}
    
    def analyze_shop_performance(self, shop_id: str) -> Dict[str, Any]:
        """
        Analyze performance metrics for a specific Etsy shop
        
        Args:
            shop_id: Etsy shop ID
            
        Returns:
            Shop performance data
        """
        # This would require additional Etsy API endpoints
        # Implementation depends on available API access
        return {
            'shop_id': shop_id,
            'analysis': 'Shop analysis requires additional API access',
            'timestamp': datetime.now().isoformat()
        }
