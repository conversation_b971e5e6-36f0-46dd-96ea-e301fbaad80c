"""
Main trend analysis engine for processing collected data
"""
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import re

from src.utils.logger import setup_logger
from src.utils.helpers import (
    calculate_trend_score, 
    normalize_keyword, 
    clean_text,
    sentiment_analysis
)
from config import Config

class TrendAnalyzer:
    """
    Main trend analysis engine that processes data from all platforms
    """
    
    def __init__(self):
        """Initialize the trend analyzer"""
        self.logger = setup_logger("trend_analyzer")
        self.platform_weights = {
            'etsy': 0.4,      # High weight for actual marketplace data
            'tiktok': 0.35,   # High weight for viral/social trends
            'google_trends': 0.25  # Supporting data for search interest
        }
        
    def analyze_trends(self, collected_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze trends from collected data across all platforms
        
        Args:
            collected_data: Dictionary containing data from all collectors
            
        Returns:
            Comprehensive trend analysis results
        """
        self.logger.info("Starting comprehensive trend analysis")
        
        # Extract platform data
        etsy_data = collected_data.get('etsy', {})
        tiktok_data = collected_data.get('tiktok', {})
        google_data = collected_data.get('google_trends', {})
        
        # Get all unique keywords across platforms
        all_keywords = self._extract_all_keywords(etsy_data, tiktok_data, google_data)
        
        # Analyze each keyword
        keyword_analyses = {}
        for keyword in all_keywords:
            analysis = self._analyze_keyword(keyword, etsy_data, tiktok_data, google_data)
            if analysis:
                keyword_analyses[keyword] = analysis
        
        # Generate micro-niche insights
        micro_niches = self._identify_micro_niches(keyword_analyses)
        
        # Create trending keywords ranking
        trending_keywords = self._rank_trending_keywords(keyword_analyses)
        
        # Generate platform-specific insights
        platform_insights = self._generate_platform_insights(etsy_data, tiktok_data, google_data)
        
        # Create final analysis report
        analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'analysis_period': Config.ANALYSIS_PERIOD_HOURS,
            'total_keywords_analyzed': len(keyword_analyses),
            'trending_keywords': trending_keywords[:Config.TOP_TRENDS_COUNT],
            'micro_niches': micro_niches,
            'platform_insights': platform_insights,
            'keyword_details': keyword_analyses,
            'recommendations': self._generate_recommendations(trending_keywords, micro_niches)
        }
        
        self.logger.info(f"Analysis complete. Found {len(trending_keywords)} trending keywords")
        return analysis_results
    
    def _extract_all_keywords(self, etsy_data: Dict, tiktok_data: Dict, google_data: Dict) -> List[str]:
        """Extract all unique keywords from platform data"""
        keywords = set()
        
        # From Etsy data
        if etsy_data and 'keywords' in etsy_data:
            keywords.update(etsy_data['keywords'].keys())
        
        # From TikTok data
        if tiktok_data and 'keywords' in tiktok_data:
            keywords.update(tiktok_data['keywords'].keys())
        
        # From Google Trends data
        if google_data and 'keywords' in google_data:
            keywords.update(google_data['keywords'].keys())
        
        # Add related keywords from hashtags and tags
        keywords.update(self._extract_related_keywords(etsy_data, tiktok_data))
        
        return list(keywords)
    
    def _extract_related_keywords(self, etsy_data: Dict, tiktok_data: Dict) -> List[str]:
        """Extract related keywords from tags and hashtags"""
        related_keywords = set()
        
        # From Etsy tags
        if etsy_data and 'keywords' in etsy_data:
            for keyword_data in etsy_data['keywords'].values():
                for tag_info in keyword_data.get('trending_tags', []):
                    tag = normalize_keyword(tag_info['tag'])
                    if len(tag) > 2 and tag not in Config.PRODUCT_CATEGORIES:
                        related_keywords.add(tag)
        
        # From TikTok hashtags
        if tiktok_data:
            # From hashtag data
            if 'hashtag_data' in tiktok_data:
                for hashtag in tiktok_data['hashtag_data'].keys():
                    normalized = normalize_keyword(hashtag)
                    if len(normalized) > 2:
                        related_keywords.add(normalized)
            
            # From video hashtags
            if 'keywords' in tiktok_data:
                for keyword_data in tiktok_data['keywords'].values():
                    for hashtag_info in keyword_data.get('trending_hashtags', []):
                        hashtag = normalize_keyword(hashtag_info['hashtag'])
                        if len(hashtag) > 2:
                            related_keywords.add(hashtag)
        
        return list(related_keywords)
    
    def _analyze_keyword(self, keyword: str, etsy_data: Dict, tiktok_data: Dict, google_data: Dict) -> Optional[Dict[str, Any]]:
        """
        Analyze a specific keyword across all platforms
        
        Args:
            keyword: Keyword to analyze
            etsy_data, tiktok_data, google_data: Platform data
            
        Returns:
            Keyword analysis results
        """
        analysis = {
            'keyword': keyword,
            'normalized_keyword': normalize_keyword(keyword),
            'platforms': [],
            'scores': {},
            'metrics': {},
            'trend_indicators': {}
        }
        
        # Analyze Etsy data
        etsy_analysis = self._analyze_etsy_keyword(keyword, etsy_data)
        if etsy_analysis:
            analysis['platforms'].append('etsy')
            analysis['scores']['etsy'] = etsy_analysis['score']
            analysis['metrics']['etsy'] = etsy_analysis['metrics']
            analysis['trend_indicators']['etsy'] = etsy_analysis['indicators']
        
        # Analyze TikTok data
        tiktok_analysis = self._analyze_tiktok_keyword(keyword, tiktok_data)
        if tiktok_analysis:
            analysis['platforms'].append('tiktok')
            analysis['scores']['tiktok'] = tiktok_analysis['score']
            analysis['metrics']['tiktok'] = tiktok_analysis['metrics']
            analysis['trend_indicators']['tiktok'] = tiktok_analysis['indicators']
        
        # Analyze Google Trends data
        google_analysis = self._analyze_google_keyword(keyword, google_data)
        if google_analysis:
            analysis['platforms'].append('google_trends')
            analysis['scores']['google_trends'] = google_analysis['score']
            analysis['metrics']['google_trends'] = google_analysis['metrics']
            analysis['trend_indicators']['google_trends'] = google_analysis['indicators']
        
        # Calculate composite score
        if analysis['platforms']:
            analysis['composite_score'] = self._calculate_composite_score(analysis['scores'])
            analysis['trend_strength'] = self._assess_trend_strength(analysis)
            analysis['market_potential'] = self._assess_market_potential(analysis)
            return analysis
        
        return None
    
    def _analyze_etsy_keyword(self, keyword: str, etsy_data: Dict) -> Optional[Dict[str, Any]]:
        """Analyze keyword performance on Etsy"""
        if not etsy_data or 'keywords' not in etsy_data:
            return None
        
        keyword_data = etsy_data['keywords'].get(keyword)
        if not keyword_data:
            return None
        
        total_results = keyword_data.get('total_results', 0)
        avg_price = keyword_data.get('avg_price', 0)
        avg_views = keyword_data.get('avg_views', 0)
        trending_tags = keyword_data.get('trending_tags', [])
        
        # Calculate Etsy-specific score
        score = 0.0
        if total_results > 0:
            # Normalize metrics
            results_score = min(1.0, total_results / 1000)  # 1000 results = max
            price_score = min(1.0, avg_price / 50)  # $50 = max reasonable price
            views_score = min(1.0, avg_views / 1000)  # 1000 views = max
            
            score = (results_score * 0.4) + (price_score * 0.3) + (views_score * 0.3)
        
        return {
            'score': score,
            'metrics': {
                'total_results': total_results,
                'avg_price': avg_price,
                'avg_views': avg_views,
                'tag_count': len(trending_tags)
            },
            'indicators': {
                'market_saturation': 'high' if total_results > 500 else 'medium' if total_results > 100 else 'low',
                'price_range': 'premium' if avg_price > 30 else 'standard' if avg_price > 15 else 'budget',
                'engagement': 'high' if avg_views > 500 else 'medium' if avg_views > 100 else 'low'
            }
        }
    
    def _analyze_tiktok_keyword(self, keyword: str, tiktok_data: Dict) -> Optional[Dict[str, Any]]:
        """Analyze keyword performance on TikTok"""
        if not tiktok_data:
            return None
        
        # Check direct keyword data
        keyword_data = None
        if 'keywords' in tiktok_data:
            keyword_data = tiktok_data['keywords'].get(keyword)
        
        # Check hashtag data
        hashtag_data = None
        if 'hashtag_data' in tiktok_data:
            hashtag_data = tiktok_data['hashtag_data'].get(keyword)
        
        if not keyword_data and not hashtag_data:
            return None
        
        total_videos = 0
        avg_engagement = 0
        video_count = 0
        view_count = 0
        
        if keyword_data:
            total_videos = keyword_data.get('total_videos', 0)
            avg_engagement = keyword_data.get('avg_engagement', 0)
        
        if hashtag_data:
            video_count = hashtag_data.get('video_count', 0)
            view_count = hashtag_data.get('view_count', 0)
        
        # Calculate TikTok-specific score
        score = 0.0
        if total_videos > 0 or video_count > 0:
            videos_score = min(1.0, max(total_videos, video_count) / 1000)
            engagement_score = min(1.0, avg_engagement / 10000)
            views_score = min(1.0, view_count / 1000000) if view_count > 0 else 0
            
            score = (videos_score * 0.4) + (engagement_score * 0.4) + (views_score * 0.2)
        
        return {
            'score': score,
            'metrics': {
                'total_videos': max(total_videos, video_count),
                'avg_engagement': avg_engagement,
                'view_count': view_count
            },
            'indicators': {
                'viral_potential': 'high' if avg_engagement > 5000 else 'medium' if avg_engagement > 1000 else 'low',
                'content_volume': 'high' if max(total_videos, video_count) > 500 else 'medium' if max(total_videos, video_count) > 100 else 'low'
            }
        }
    
    def _analyze_google_keyword(self, keyword: str, google_data: Dict) -> Optional[Dict[str, Any]]:
        """Analyze keyword performance on Google Trends"""
        if not google_data or 'keywords' not in google_data:
            return None
        
        keyword_data = google_data['keywords'].get(keyword)
        if not keyword_data:
            return None
        
        current_interest = keyword_data.get('current_interest', 0)
        growth_rate = keyword_data.get('growth_rate', 0)
        trend_score = keyword_data.get('trend_score', 0)
        
        return {
            'score': trend_score,
            'metrics': {
                'current_interest': current_interest,
                'growth_rate': growth_rate,
                'trend_direction': keyword_data.get('trend_direction', 'stable')
            },
            'indicators': {
                'search_momentum': 'rising' if growth_rate > 20 else 'stable' if growth_rate > -10 else 'declining',
                'search_volume': 'high' if current_interest > 50 else 'medium' if current_interest > 20 else 'low'
            }
        }

    def _calculate_composite_score(self, platform_scores: Dict[str, float]) -> float:
        """
        Calculate weighted composite score across platforms

        Args:
            platform_scores: Dictionary of platform scores

        Returns:
            Composite score between 0 and 1
        """
        total_score = 0.0
        total_weight = 0.0

        for platform, score in platform_scores.items():
            weight = self.platform_weights.get(platform, 0.0)
            total_score += score * weight
            total_weight += weight

        return total_score / total_weight if total_weight > 0 else 0.0

    def _assess_trend_strength(self, analysis: Dict[str, Any]) -> str:
        """Assess overall trend strength"""
        composite_score = analysis.get('composite_score', 0)
        platform_count = len(analysis.get('platforms', []))

        if composite_score > 0.7 and platform_count >= 2:
            return 'strong'
        elif composite_score > 0.5 and platform_count >= 2:
            return 'moderate'
        elif composite_score > 0.3:
            return 'weak'
        else:
            return 'minimal'

    def _assess_market_potential(self, analysis: Dict[str, Any]) -> str:
        """Assess market potential for the keyword"""
        etsy_metrics = analysis.get('metrics', {}).get('etsy', {})
        tiktok_metrics = analysis.get('metrics', {}).get('tiktok', {})

        # High potential: Good Etsy results but not oversaturated + TikTok engagement
        etsy_results = etsy_metrics.get('total_results', 0)
        tiktok_engagement = tiktok_metrics.get('avg_engagement', 0)

        if 100 <= etsy_results <= 500 and tiktok_engagement > 1000:
            return 'high'
        elif etsy_results > 0 and (tiktok_engagement > 500 or etsy_results < 1000):
            return 'medium'
        else:
            return 'low'

    def _identify_micro_niches(self, keyword_analyses: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Identify micro-niche opportunities

        Args:
            keyword_analyses: All keyword analysis results

        Returns:
            List of micro-niche opportunities
        """
        micro_niches = []

        for keyword, analysis in keyword_analyses.items():
            # Criteria for micro-niche:
            # 1. Medium to high trend strength
            # 2. Not oversaturated on Etsy (< 500 results)
            # 3. Growing search interest
            # 4. Some social media presence

            trend_strength = analysis.get('trend_strength', 'minimal')
            etsy_metrics = analysis.get('metrics', {}).get('etsy', {})
            google_metrics = analysis.get('metrics', {}).get('google_trends', {})

            etsy_results = etsy_metrics.get('total_results', 0)
            growth_rate = google_metrics.get('growth_rate', 0)

            if (trend_strength in ['moderate', 'strong'] and
                50 <= etsy_results <= 500 and
                growth_rate > 0):

                micro_niches.append({
                    'keyword': keyword,
                    'trend_strength': trend_strength,
                    'market_potential': analysis.get('market_potential', 'low'),
                    'composite_score': analysis.get('composite_score', 0),
                    'platforms': analysis.get('platforms', []),
                    'opportunity_score': self._calculate_opportunity_score(analysis)
                })

        # Sort by opportunity score
        return sorted(micro_niches, key=lambda x: x['opportunity_score'], reverse=True)

    def _calculate_opportunity_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate opportunity score for micro-niche identification"""
        composite_score = analysis.get('composite_score', 0)
        market_potential = analysis.get('market_potential', 'low')
        platform_count = len(analysis.get('platforms', []))

        # Base score from composite
        score = composite_score

        # Bonus for market potential
        potential_bonus = {'high': 0.3, 'medium': 0.15, 'low': 0.0}
        score += potential_bonus.get(market_potential, 0)

        # Bonus for multi-platform presence
        if platform_count >= 3:
            score += 0.2
        elif platform_count >= 2:
            score += 0.1

        return min(1.0, score)

    def _rank_trending_keywords(self, keyword_analyses: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Rank keywords by trending potential

        Args:
            keyword_analyses: All keyword analysis results

        Returns:
            Ranked list of trending keywords
        """
        trending = []

        for keyword, analysis in keyword_analyses.items():
            if analysis.get('composite_score', 0) >= Config.TREND_THRESHOLD:
                trending_item = {
                    'keyword': keyword,
                    'composite_score': analysis.get('composite_score', 0),
                    'trend_strength': analysis.get('trend_strength', 'minimal'),
                    'market_potential': analysis.get('market_potential', 'low'),
                    'platforms': analysis.get('platforms', []),
                    'platform_scores': analysis.get('scores', {}),
                    'key_metrics': self._extract_key_metrics(analysis)
                }
                trending.append(trending_item)

        # Sort by composite score
        return sorted(trending, key=lambda x: x['composite_score'], reverse=True)

    def _extract_key_metrics(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key metrics for reporting"""
        metrics = {}

        # Etsy metrics
        etsy_metrics = analysis.get('metrics', {}).get('etsy', {})
        if etsy_metrics:
            metrics['etsy_results'] = etsy_metrics.get('total_results', 0)
            metrics['etsy_avg_price'] = etsy_metrics.get('avg_price', 0)

        # TikTok metrics
        tiktok_metrics = analysis.get('metrics', {}).get('tiktok', {})
        if tiktok_metrics:
            metrics['tiktok_videos'] = tiktok_metrics.get('total_videos', 0)
            metrics['tiktok_engagement'] = tiktok_metrics.get('avg_engagement', 0)

        # Google metrics
        google_metrics = analysis.get('metrics', {}).get('google_trends', {})
        if google_metrics:
            metrics['google_interest'] = google_metrics.get('current_interest', 0)
            metrics['google_growth'] = google_metrics.get('growth_rate', 0)

        return metrics

    def _generate_platform_insights(self, etsy_data: Dict, tiktok_data: Dict, google_data: Dict) -> Dict[str, Any]:
        """Generate insights specific to each platform"""
        insights = {}

        # Etsy insights
        if etsy_data and 'keywords' in etsy_data:
            etsy_insights = {
                'total_keywords_searched': len(etsy_data['keywords']),
                'avg_results_per_keyword': 0,
                'price_trends': {},
                'popular_categories': []
            }

            total_results = 0
            prices = []

            for keyword_data in etsy_data['keywords'].values():
                total_results += keyword_data.get('total_results', 0)
                price = keyword_data.get('avg_price', 0)
                if price > 0:
                    prices.append(price)

            if etsy_data['keywords']:
                etsy_insights['avg_results_per_keyword'] = total_results / len(etsy_data['keywords'])

            if prices:
                etsy_insights['price_trends'] = {
                    'avg_price': sum(prices) / len(prices),
                    'min_price': min(prices),
                    'max_price': max(prices)
                }

            insights['etsy'] = etsy_insights

        # TikTok insights
        if tiktok_data:
            tiktok_insights = {
                'trending_hashtags': [],
                'viral_potential_keywords': [],
                'engagement_trends': {}
            }

            # Extract trending hashtags
            if 'hashtag_data' in tiktok_data:
                hashtag_scores = []
                for hashtag, data in tiktok_data['hashtag_data'].items():
                    score = data.get('trend_score', 0)
                    if score > 0.5:
                        hashtag_scores.append({'hashtag': hashtag, 'score': score})

                tiktok_insights['trending_hashtags'] = sorted(
                    hashtag_scores, key=lambda x: x['score'], reverse=True
                )[:10]

            insights['tiktok'] = tiktok_insights

        # Google Trends insights
        if google_data and 'keywords' in google_data:
            google_insights = {
                'rising_searches': [],
                'declining_searches': [],
                'stable_searches': []
            }

            for keyword, data in google_data['keywords'].items():
                trend_direction = data.get('trend_direction', 'stable')
                growth_rate = data.get('growth_rate', 0)

                keyword_info = {'keyword': keyword, 'growth_rate': growth_rate}

                if trend_direction == 'rising':
                    google_insights['rising_searches'].append(keyword_info)
                elif trend_direction == 'falling':
                    google_insights['declining_searches'].append(keyword_info)
                else:
                    google_insights['stable_searches'].append(keyword_info)

            # Sort by growth rate
            for category in ['rising_searches', 'declining_searches']:
                google_insights[category] = sorted(
                    google_insights[category],
                    key=lambda x: abs(x['growth_rate']),
                    reverse=True
                )[:10]

            insights['google_trends'] = google_insights

        return insights

    def _generate_recommendations(self, trending_keywords: List[Dict], micro_niches: List[Dict]) -> Dict[str, Any]:
        """Generate actionable recommendations"""
        recommendations = {
            'immediate_opportunities': [],
            'design_suggestions': [],
            'keyword_strategies': [],
            'platform_focus': {}
        }

        # Immediate opportunities (top trending with good market potential)
        for keyword_data in trending_keywords[:5]:
            if keyword_data.get('market_potential') in ['high', 'medium']:
                recommendations['immediate_opportunities'].append({
                    'keyword': keyword_data['keyword'],
                    'reason': f"High trend score ({keyword_data['composite_score']:.2f}) with {keyword_data['market_potential']} market potential",
                    'platforms': keyword_data['platforms'],
                    'suggested_products': self._suggest_products(keyword_data['keyword'])
                })

        # Design suggestions based on trending themes
        design_themes = self._extract_design_themes(trending_keywords)
        recommendations['design_suggestions'] = design_themes

        # Keyword strategies
        recommendations['keyword_strategies'] = [
            "Focus on long-tail keywords with 3-4 words for better targeting",
            "Combine trending keywords with product types (t-shirt, hoodie, mug)",
            "Use seasonal and event-based keywords for timely relevance",
            "Monitor competitor keywords on Etsy for inspiration"
        ]

        # Platform focus recommendations
        platform_performance = self._analyze_platform_performance(trending_keywords)
        recommendations['platform_focus'] = platform_performance

        return recommendations

    def _suggest_products(self, keyword: str) -> List[str]:
        """Suggest product types for a keyword"""
        keyword_lower = keyword.lower()
        products = []

        # Default products
        base_products = ['t-shirt', 'hoodie', 'mug']

        # Keyword-specific suggestions
        if any(word in keyword_lower for word in ['coffee', 'tea', 'drink']):
            products.extend(['coffee mug', 'travel mug', 'tumbler'])

        if any(word in keyword_lower for word in ['mom', 'dad', 'family']):
            products.extend(['family t-shirt set', 'parent hoodie', 'family mug set'])

        if any(word in keyword_lower for word in ['funny', 'humor', 'joke']):
            products.extend(['funny t-shirt', 'sarcastic mug', 'humor hoodie'])

        return products if products else base_products

    def _extract_design_themes(self, trending_keywords: List[Dict]) -> List[str]:
        """Extract common design themes from trending keywords"""
        themes = []

        # Analyze keyword patterns
        all_keywords = [item['keyword'].lower() for item in trending_keywords[:10]]

        # Common themes
        if any('vintage' in kw for kw in all_keywords):
            themes.append("Vintage/retro aesthetic designs")

        if any(word in ' '.join(all_keywords) for word in ['mom', 'dad', 'family']):
            themes.append("Family-oriented and parental humor designs")

        if any(word in ' '.join(all_keywords) for word in ['coffee', 'tea']):
            themes.append("Coffee/tea culture and beverage-themed designs")

        if any(word in ' '.join(all_keywords) for word in ['funny', 'humor', 'sarcastic']):
            themes.append("Humorous and sarcastic text-based designs")

        return themes if themes else ["Minimalist text-based designs", "Pop culture references"]

    def _analyze_platform_performance(self, trending_keywords: List[Dict]) -> Dict[str, str]:
        """Analyze which platforms are performing best"""
        platform_scores = defaultdict(list)

        for keyword_data in trending_keywords:
            for platform, score in keyword_data.get('platform_scores', {}).items():
                platform_scores[platform].append(score)

        platform_performance = {}
        for platform, scores in platform_scores.items():
            avg_score = sum(scores) / len(scores) if scores else 0

            if avg_score > 0.7:
                performance = "Excellent - High priority for marketing and content creation"
            elif avg_score > 0.5:
                performance = "Good - Solid opportunity for growth"
            elif avg_score > 0.3:
                performance = "Moderate - Consider secondary focus"
            else:
                performance = "Low - Monitor but don't prioritize"

            platform_performance[platform] = performance

        return platform_performance
