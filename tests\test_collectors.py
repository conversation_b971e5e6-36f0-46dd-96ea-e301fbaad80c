"""
Unit tests for data collectors
"""
import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.collectors.base_collector import BaseCollector
from src.collectors.etsy_collector import EtsyCollector
from src.collectors.tiktok_collector import <PERSON><PERSON><PERSON><PERSON><PERSON>oll<PERSON>
from src.collectors.google_trends_collector import GoogleTrendsCollector

class TestBaseCollector:
    """Test base collector functionality"""
    
    def test_base_collector_initialization(self):
        """Test base collector initialization"""
        
        class TestCollector(BaseCollector):
            def _get_auth_headers(self):
                return {'Authorization': 'Bearer test'}
            
            def collect_data(self, keywords, **kwargs):
                return {'test': 'data'}
        
        collector = TestCollector("test", "api_key", 1.0)
        
        assert collector.name == "test"
        assert collector.api_key == "api_key"
        assert collector.rate_limit == 1.0
        assert collector.session is not None
    
    @patch('requests.Session.get')
    def test_make_request_success(self, mock_get):
        """Test successful API request"""
        
        class TestCollector(BaseCollector):
            def _get_auth_headers(self):
                return {}
            
            def collect_data(self, keywords, **kwargs):
                return self._make_request('http://test.com')
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'success': True}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        collector = TestCollector("test")
        result = collector.collect_data([])
        
        assert result == {'success': True}
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_make_request_failure(self, mock_get):
        """Test failed API request"""
        
        class TestCollector(BaseCollector):
            def _get_auth_headers(self):
                return {}
            
            def collect_data(self, keywords, **kwargs):
                return self._make_request('http://test.com')
        
        # Mock failed response
        mock_get.side_effect = Exception("Network error")
        
        collector = TestCollector("test")
        result = collector.collect_data([])
        
        assert result is None

class TestEtsyCollector:
    """Test Etsy collector"""
    
    def test_etsy_collector_initialization(self):
        """Test Etsy collector initialization"""
        collector = EtsyCollector("test_api_key")
        
        assert collector.name == "etsy"
        assert collector.api_key == "test_api_key"
        assert 'x-api-key' in collector._get_auth_headers()
    
    @patch('src.collectors.etsy_collector.EtsyCollector._make_request')
    def test_collect_data(self, mock_request):
        """Test Etsy data collection"""
        # Mock API response
        mock_request.return_value = {
            'results': [
                {
                    'listing_id': 123,
                    'title': 'Custom T-Shirt',
                    'price': {'amount': 2000},  # $20.00 in cents
                    'views': 150,
                    'tags': ['custom', 'tshirt'],
                    'shop': {'shop_name': 'TestShop'}
                }
            ],
            'count': 1
        }
        
        collector = EtsyCollector("test_key")
        result = collector.collect_data(['custom t-shirt'])
        
        assert result['platform'] == 'etsy'
        assert 'custom t-shirt' in result['keywords']
        assert result['summary']['successful_searches'] == 1
    
    def test_process_search_results(self):
        """Test search results processing"""
        collector = EtsyCollector("test_key")
        
        mock_data = {
            'results': [
                {
                    'listing_id': 123,
                    'title': 'Test Product',
                    'price': {'amount': 1500},
                    'views': 100,
                    'num_favorers': 10,
                    'tags': ['test', 'product'],
                    'shop': {'shop_name': 'TestShop'},
                    'creation_timestamp': 1234567890,
                    'url': 'http://test.com'
                }
            ],
            'count': 1
        }
        
        result = collector._process_search_results(mock_data, 'test')
        
        assert result['keyword'] == 'test'
        assert result['total_results'] == 1
        assert len(result['listings']) == 1
        assert result['avg_price'] == 15.0

class TestTikTokCollector:
    """Test TikTok collector"""
    
    def test_tiktok_collector_initialization(self):
        """Test TikTok collector initialization"""
        collector = TikTokCollector("test_token")
        
        assert collector.name == "tiktok"
        assert collector.api_key == "test_token"
        assert 'Authorization' in collector._get_auth_headers()
    
    @patch('src.collectors.tiktok_collector.TikTokCollector._make_request')
    def test_collect_hashtag_trends(self, mock_request):
        """Test hashtag trend collection"""
        # Mock hashtag API response
        mock_request.return_value = {
            'data': {
                'hashtag_name': 'tshirtdesign',
                'video_count': 1000,
                'view_count': 50000,
                'publish_time': '2024-01-01'
            }
        }
        
        collector = TikTokCollector("test_token")
        result = collector._get_hashtag_info('tshirtdesign')
        
        assert result['hashtag'] == 'tshirtdesign'
        assert result['video_count'] == 1000
        assert result['view_count'] == 50000
    
    def test_calculate_hashtag_trend_score(self):
        """Test hashtag trend score calculation"""
        collector = TikTokCollector("test_token")
        
        hashtag_data = {
            'video_count': 5000,
            'view_count': 25000000
        }
        
        score = collector._calculate_hashtag_trend_score(hashtag_data)
        
        assert 0 <= score <= 1
        assert isinstance(score, float)

class TestGoogleTrendsCollector:
    """Test Google Trends collector"""
    
    def test_google_trends_initialization(self):
        """Test Google Trends collector initialization"""
        collector = GoogleTrendsCollector()
        
        assert collector.name == "google_trends"
        assert collector.api_key is None
        assert collector.pytrends is not None
    
    @patch('pytrends.request.TrendReq')
    def test_collect_data(self, mock_pytrends):
        """Test Google Trends data collection"""
        # Mock pytrends
        mock_trends_instance = Mock()
        mock_pytrends.return_value = mock_trends_instance
        
        # Mock interest over time data
        import pandas as pd
        mock_interest_data = pd.DataFrame({
            'test keyword': [10, 20, 30, 40, 50],
            'isPartial': [False, False, False, False, True]
        })
        mock_interest_data.index = pd.date_range('2024-01-01', periods=5, freq='D')
        
        mock_trends_instance.interest_over_time.return_value = mock_interest_data
        mock_trends_instance.interest_by_region.return_value = pd.DataFrame({
            'test keyword': {'California': 100, 'Texas': 80}
        })
        
        collector = GoogleTrendsCollector()
        collector.pytrends = mock_trends_instance
        
        result = collector._get_trends_data(['test keyword'])
        
        assert 'test keyword' in result
        assert 'current_interest' in result['test keyword']
    
    def test_calculate_trend_score(self):
        """Test trend score calculation"""
        collector = GoogleTrendsCollector()
        
        score = collector._calculate_trend_score(75, 25)
        
        assert 0 <= score <= 1
        assert isinstance(score, float)
    
    def test_identify_trending_keywords(self):
        """Test trending keyword identification"""
        collector = GoogleTrendsCollector()
        
        keywords_data = {
            'trending_keyword': {
                'growth_rate': 50,
                'current_interest': 80,
                'trend_score': 0.8
            },
            'stable_keyword': {
                'growth_rate': 5,
                'current_interest': 30,
                'trend_score': 0.4
            }
        }
        
        trending = collector._identify_trending_keywords(keywords_data)
        
        assert len(trending) == 1
        assert trending[0]['keyword'] == 'trending_keyword'

# Integration tests
class TestCollectorIntegration:
    """Test collector integration"""
    
    def test_all_collectors_have_required_methods(self):
        """Test that all collectors implement required methods"""
        collectors = [
            EtsyCollector("test"),
            TikTokCollector("test"),
            GoogleTrendsCollector()
        ]
        
        for collector in collectors:
            assert hasattr(collector, 'collect_data')
            assert hasattr(collector, '_get_auth_headers')
            assert hasattr(collector, 'get_collection_summary')
    
    def test_collector_error_handling(self):
        """Test collector error handling"""
        # Test with invalid API keys
        collectors = [
            EtsyCollector("invalid_key"),
            TikTokCollector("invalid_key")
        ]
        
        for collector in collectors:
            # Should not raise exception, should return empty/error result
            try:
                result = collector.collect_data(['test'])
                assert isinstance(result, dict)
            except Exception as e:
                pytest.fail(f"Collector {collector.name} should handle errors gracefully: {e}")

if __name__ == '__main__':
    pytest.main([__file__])
