# US Micro-Niche Trend Tracker

ABD pazarına odaklı mikro-niche tişört, hoodie ve mug tasarımı trendlerini anlık takip eden Python sistemi.

## 🎯 Özellikler

- **Çoklu Veri Kaynağı**: Etsy, TikTok ve Google Trends verilerini toplar
- **Akıllı Analiz**: Trend skorları hesaplar ve mikro-niche fırsatları belirler
- **Otomatik Raporlama**: CSV, JSON, Excel ve özet raporları üretir
- **Çoklu Bildirim**: Slack, Discord ve e-posta uyarıları
- **Zamanlanmış Çalışma**: Otomatik veri toplama ve analiz
- **Modüler Yapı**: Kolay genişletilebilir ve özelleştirilebilir

## 🚀 Hızlı Başlangıç

### 1. Kurulum

```bash
# Repository'yi klonlayın
git clone <repository-url>
cd erank-deneme2

# Sanal ortam oluşturun
python -m venv venv
source venv/bin/activate  # Linux/Mac
# veya
venv\Scripts\activate  # Windows

# Bağımlılıkları yükleyin
pip install -r requirements.txt
```

### 2. Konfigürasyon

```bash
# .env dosyasını oluşturun
cp .env.example .env

# API anahtarlarınızı ekleyin
nano .env
```

Gerekli API anahtarları:
- **Etsy API Key**: [Etsy Developer Portal](https://developers.etsy.com/)
- **TikTok API Key**: [TikTok for Developers](https://developers.tiktok.com/)
- **Slack Bot Token**: [Slack API](https://api.slack.com/)
- **Discord Bot Token**: [Discord Developer Portal](https://discord.com/developers/)

### 3. Kullanım

#### Tek Seferlik Analiz
```bash
python main.py --mode run
```

#### Özel Anahtar Kelimelerle
```bash
python main.py --mode run --keywords "custom hoodie" "funny mug" "vintage tee"
```

#### Zamanlanmış Çalışma
```bash
python main.py --mode schedule
```

#### Sistem Testi
```bash
python main.py --mode test
```

## 📊 Çıktı Formatları

### 1. JSON Raporu
Tüm analiz verilerini içeren kapsamlı JSON dosyası.

### 2. CSV Raporları
- `trending_keywords_YYYYMMDD_HHMMSS.csv`: Trend olan anahtar kelimeler
- `micro_niches_YYYYMMDD_HHMMSS.csv`: Mikro-niche fırsatları
- `platform_insights_YYYYMMDD_HHMMSS.csv`: Platform bazlı içgörüler

### 3. Excel Raporu
Çoklu sayfa içeren kapsamlı Excel dosyası.

### 4. Özet Raporu
İnsan tarafından okunabilir metin formatında özet.

## 🔧 Konfigürasyon

### Ana Ayarlar (`config.py`)

```python
# Trend analiz ayarları
TREND_THRESHOLD = 0.7  # Minimum trend skoru (0-1)
MIN_SEARCH_VOLUME = 100  # Minimum arama hacmi
ANALYSIS_PERIOD_HOURS = 24  # Analiz periyodu (saat)
TOP_TRENDS_COUNT = 20  # Rapor edilecek trend sayısı

# Veri toplama ayarları
MAX_ETSY_RESULTS = 500
MAX_TIKTOK_RESULTS = 100
SCRAPING_DELAY = 2  # İstekler arası gecikme (saniye)

# Zamanlama
COLLECTION_INTERVAL_HOURS = 6  # Veri toplama sıklığı
REPORT_INTERVAL_HOURS = 24  # Rapor üretme sıklığı
```

### Platform Ayarları

#### Etsy
- Ürün kategorileri: Giyim, ev eşyaları
- Arama parametreleri: ABD pazarı, minimum fiyat
- Rate limiting: 10 istek/saniye

#### TikTok
- Hedef hashtagler: #tshirtdesign, #customhoodie, vb.
- Video analizi: Son 7 gün
- Engagement metrikleri: Beğeni, yorum, paylaşım

#### Google Trends
- Coğrafya: ABD
- Zaman aralığı: Son 7 gün
- Kategori: Tüm kategoriler

## 📈 Trend Analiz Algoritması

### 1. Veri Toplama
Her platform için ayrı collector modülleri:
- **EtsyCollector**: Ürün arama, satış verileri
- **TikTokCollector**: Hashtag trendleri, video metrikleri
- **GoogleTrendsCollector**: Arama trendleri, bölgesel veriler

### 2. Trend Skoru Hesaplama
```python
composite_score = (etsy_score * 0.4) + (tiktok_score * 0.35) + (google_score * 0.25)
```

#### Platform Skorları:
- **Etsy**: Arama sonucu sayısı + ortalama satış + görüntülenme
- **TikTok**: Video sayısı + engagement + görüntülenme
- **Google**: Arama ilgisi + büyüme oranı

### 3. Mikro-Niche Belirleme
Kriterler:
- Orta/yüksek trend gücü
- Etsy'de aşırı doygunluk yok (< 500 sonuç)
- Pozitif büyüme trendi
- Sosyal medya varlığı

## 🔔 Bildirim Sistemi

### Slack Entegrasyonu
```python
# Slack bot token ile otomatik mesaj gönderimi
# Rapor dosyalarının otomatik yüklenmesi
# Zengin format ile trend özetleri
```

### Discord Entegrasyonu
```python
# Webhook veya bot token ile bildirim
# Embed formatında trend raporları
# Renk kodlu uyarılar
```

### E-posta Bildirimleri
```python
# HTML formatında detaylı raporlar
# Rapor dosyalarının ek olarak gönderilmesi
# Çoklu alıcı desteği
```

## 🧪 Test Sistemi

### Unit Testler
```bash
# Tüm testleri çalıştır
pytest tests/

# Belirli modül testleri
pytest tests/test_collectors.py
pytest tests/test_analyzer.py
pytest tests/test_reporting.py
```

### Sistem Testi
```bash
python main.py --mode test
```

Test edilen bileşenler:
- ✅ Veri toplayıcıları
- ✅ Trend analiz motoru
- ✅ Rapor üretici
- ✅ Bildirim sistemi

## 📁 Proje Yapısı

```
erank-deneme2/
├── main.py                 # Ana uygulama
├── config.py              # Konfigürasyon
├── requirements.txt       # Python bağımlılıkları
├── .env.example          # Örnek çevre değişkenleri
├── README.md             # Bu dosya
├── src/
│   ├── collectors/       # Veri toplama modülleri
│   │   ├── base_collector.py
│   │   ├── etsy_collector.py
│   │   ├── tiktok_collector.py
│   │   └── google_trends_collector.py
│   ├── analyzer/         # Trend analiz motoru
│   │   └── trend_analyzer.py
│   ├── reporting/        # Rapor üretimi
│   │   └── report_generator.py
│   ├── notifications/    # Bildirim sistemi
│   │   └── notification_manager.py
│   └── utils/           # Yardımcı araçlar
│       ├── logger.py
│       └── helpers.py
├── tests/               # Test dosyaları
├── output/             # Üretilen raporlar
├── logs/               # Log dosyaları
└── data/               # Geçici veri dosyaları
```

## 🔍 Örnek Çıktı

### Trend Analiz Özeti
```
=== US MICRO-NICHE TREND ANALYSIS SUMMARY ===

Generated: 2024-01-15 14:30:00
Analysis Period: 24 hours
Total Keywords Analyzed: 45

TOP TRENDING KEYWORDS
1. vintage mom shirt
   Score: 0.847 | Strength: strong | Potential: high
   Platforms: etsy, tiktok, google_trends

2. custom dog hoodie
   Score: 0.792 | Strength: strong | Potential: medium
   Platforms: etsy, tiktok

MICRO-NICHE OPPORTUNITIES
1. plant mom mug
   Opportunity Score: 0.823
   Market Potential: high

2. sarcastic teacher shirt
   Opportunity Score: 0.756
   Market Potential: medium
```

## 🚨 Sorun Giderme

### Yaygın Hatalar

1. **API Rate Limiting**
   ```
   Çözüm: config.py'de SCRAPING_DELAY değerini artırın
   ```

2. **Eksik API Anahtarları**
   ```
   Çözüm: .env dosyasını kontrol edin, gerekli anahtarları ekleyin
   ```

3. **Bildirim Gönderimi Başarısız**
   ```
   Çözüm: Slack/Discord token'larını ve kanal ayarlarını kontrol edin
   ```

### Log Dosyaları
```bash
# Ana uygulama logları
tail -f logs/trend_tracker_YYYYMMDD.log

# Collector logları
tail -f logs/collector_etsy_YYYYMMDD.log
tail -f logs/collector_tiktok_YYYYMMDD.log
```

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 Destek

Sorularınız için:
- GitHub Issues
- E-posta: [<EMAIL>]
- Slack: #trend-tracker-support
